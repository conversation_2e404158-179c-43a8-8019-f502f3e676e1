/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: JarDecompressHelper.kt
 ** Description: Decompress jar file
 ** Version: 1.0
 ** Date: 2020/3/10
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.decompress

import android.util.ArrayMap
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import org.apache.commons.compress.archivers.jar.JarArchiveEntry
import org.apache.commons.compress.archivers.jar.JarArchiveInputStream
import org.apache.commons.io.FileUtils
import java.io.*
import java.util.jar.JarFile

class JarDecompressHelper : DecompressHelper<JarDecompressFile>() {
    companion object {
        private const val TAG = "JarDecompressHelper"
        private const val COPY_BUF_SIZE = 524288;
    }

    override fun createDecompressDirFile(path: String): JarDecompressFile {
        return JarDecompressFile(null).easyCreateDirFile(path) as JarDecompressFile
    }

    override fun internalPreview(sourceFile: BaseFileBean): Pair<Int, MutableMap<String, MutableList<JarDecompressFile>?>?> {
        val sourceJavaFile = File(sourceFile.mData)
        var inputStream: JarArchiveInputStream? = null
        try {
            inputStream = JarArchiveInputStream(BufferedInputStream(FileInputStream(sourceJavaFile)))
            var entry: JarArchiveEntry? = null
            var compressFiles = ArrayMap<String, MutableList<JarDecompressFile>?>()
            while (inputStream.nextJarEntry.also { entry = it } != null) {
                if (compressFiles.size >= MAX_COMPRESS_FILE_NUM) {
                    compressFiles.clear()
                    return Pair(PREVIEW_OVERCOUNT, null)
                }

                if (isValidPreviewFileName(entry?.name)) {
                    val f = JarDecompressFile(entry)
                    createPreviewFileTree(f, compressFiles)
                }
            }
            return Pair(PREVIEW_SUCCESS, compressFiles)
        } catch (e: Exception) {
            Log.e(TAG, "jar file internalPreview failed: ${e.message}")
            return Pair(PREVIEW_FAILED, null)
        } finally {
            quietClose(inputStream)
        }
    }

    override fun internalDecompress(sourceFile: BaseFileBean, destParentFile: BaseFileBean, password: String?,
                                    selectFiles: MutableList<JarDecompressFile>?, listener: OnDecompressListener?): Boolean? {
        Log.d(TAG, "decompress file path = ${sourceFile.mData} outPutDir = ${destParentFile.mData}")
        val sourceJavaFile = File(sourceFile.mData)
        return if (sourceJavaFile.exists() && isCancelled().not()) {
            val jarFile = JarFile(sourceJavaFile)
            var archiveInputStream: JarArchiveInputStream? = null
            var result = true
            try {
                val hasSelectFile = selectFiles?.isNotEmpty() ?: false
                archiveInputStream = JarArchiveInputStream(BufferedInputStream(FileInputStream(sourceJavaFile)))
                var entry: JarArchiveEntry?
                var totalSize = 0L
                val dealList = arrayListOf<JarArchiveEntry>()
                while ((null != archiveInputStream.nextJarEntry.also { entry = it })) {
                    if (isCancelled()) {
                        Log.d(TAG, "decompressNormalFile failed: cancelled when calculate")
                        return false
                    }
                    entry?.let {
                        if (!hasSelectFile || checkCanDecompress(it.name, it.isDirectory, selectFiles!!)) {
                            totalSize += it.size
                            dealList.add(it)
                        }
                    }
                }

                saveDecompressTotalSize(totalSize)
                dealList.forEach {
                    saveArchiveEntry(jarFile, it, destParentFile.mData!!)
                    if (isCancelled()) {
                        Log.d(TAG, "internalDecompress failed: cancelled when decompress")
                        return false
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "decompress file failed: ${e.message}")
                result = false
            } finally {
                quietClose(archiveInputStream)
                quietClose(jarFile)
            }
            result
        } else false
    }

    private fun saveArchiveEntry(jarFile: JarFile, entry: JarArchiveEntry, parentPath: String) {
        val parentPath = parentPath.plus(if (parentPath.endsWith(File.separator)) "" else File.separator)
        val file = File(parentPath, entry.name)
        if (entry.isDirectory) {
            forceMkdirs(file)
        } else {
            decompressCopy(jarFile, entry, file)
        }
    }

    private fun decompressCopy(jarFile: JarFile, entry: JarArchiveEntry, javaFile: File) {
        var inputStream: InputStream? = null
        var outputStream: OutputStream? = null
        var bufferedInputStream: BufferedInputStream? = null
        var bufferedOutputStream: BufferedOutputStream? = null

        val buffer = ByteArray(COPY_BUF_SIZE)
        var n = 0
        try {
            inputStream = jarFile.getInputStream(entry)
            outputStream = FileUtils.openOutputStream(javaFile)
            if (entry.size > COPY_BUF_SIZE) {
                bufferedInputStream = BufferedInputStream(inputStream, COPY_BUF_SIZE)
                bufferedOutputStream = BufferedOutputStream(outputStream, COPY_BUF_SIZE)
            } else {
                bufferedInputStream = BufferedInputStream(inputStream)
                bufferedOutputStream = BufferedOutputStream(outputStream)
            }
            while ((-1 != bufferedInputStream.read(buffer).also { n = it })) {
                if (isCancelled()) {
                    val delResult = javaFile.delete()
                    Log.d(TAG, "decompressCopy: Delete file when cancel: [$delResult], ${javaFile.name}")
                    break
                }
                bufferedOutputStream.write(buffer, 0, n)
                updateDecompressProgress(n.toLong())
            }
        } catch (e: Exception) {
            Log.e(TAG, "decompressCopy failed: ${e.message}")
        } finally {
            quietClose(bufferedInputStream)
            quietClose(bufferedOutputStream)
            quietClose(inputStream)
            quietClose(outputStream)
        }
    }

    //jar not support password
    override fun internalIsEncrypted(sourceFile: BaseFileBean): Boolean {
        return false
    }

    //jar not support password
    override fun internalVerifyPassword(sourceFile: BaseFileBean, password: String?): Boolean {
        return false
    }

    override  fun checkIsSupport(sourceFile: BaseFileBean): Boolean {
        return true
    }
}