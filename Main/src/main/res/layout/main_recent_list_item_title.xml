<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/child_title_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dimen_16dp">

    <TextView
        android:id="@+id/child_left_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:ellipsize="end"
        android:maxWidth="@dimen/recent_folder_name_max_witdh"
        android:singleLine="true"
        android:textSize="16sp"
        android:fontFamily="sans-serif-medium"
        android:textColor="?attr/couiColorLabelPrimary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/child_right_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_10dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textAppearance="@style/couiTextButtonS"
        android:textColor="?attr/couiColorLabelSecondary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/child_left_text"
        app:layout_constraintTop_toTopOf="parent" />

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/recent_checkbox_end_margin"
        android:clickable="false"
        android:focusable="false"
        android:paddingStart="@dimen/dimen_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>