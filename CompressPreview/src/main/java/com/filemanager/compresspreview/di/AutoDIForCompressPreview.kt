/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileChoose
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.filemanager.compresspreview.di

import com.filemanager.compresspreview.CompressPreviewApi
import com.oplus.filemanager.interfaze.compresspreview.ICompressPreview
import org.koin.dsl.module

object AutoDIForCompressPreview {

    val compressPreviewModule = module {
        single<ICompressPreview>(createdAtStart = true) {
            CompressPreviewApi
        }
    }
}