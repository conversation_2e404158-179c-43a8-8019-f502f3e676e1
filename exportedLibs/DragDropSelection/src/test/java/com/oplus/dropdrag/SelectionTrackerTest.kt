package com.oplus.dropdrag

import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.recycleview.*
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.clearAllMocks
import org.junit.Assert.*
import org.junit.Before
import org.junit.After
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import org.junit.runners.Parameterized.Parameters

/**
 * SelectionTracker的单元测试类
 * 测试SelectionTracker及其相关组件的功能
 */
class SelectionTrackerTest {

    /**
     * 在每个测试方法执行后清理mock对象
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    /**
     * 测试SelectionTracker的伴生对象功能
     */
    inner class CompanionObjectTests {
        /**
         * 测试isValidKey方法对无效int key的识别
         */
        @Test
        fun `isValidKey should return false for invalid int keys`() {
            assertFalse(SelectionTracker.isValidKey(-1))
            assertFalse(SelectionTracker.isValidKey(-2))
        }

        /**
         * 测试isValidKey方法对无效long key的识别
         */
        @Test
        fun `isValidKey should return false for invalid long keys`() {
            assertFalse(SelectionTracker.isValidKey(-1L))
            assertFalse(SelectionTracker.isValidKey(-2L))
        }

        /**
         * 测试isValidKey方法对有效key的识别
         */
        @Test
        fun `isValidKey should return true for valid keys`() {
            assertTrue(SelectionTracker.isValidKey(1))
            assertTrue(SelectionTracker.isValidKey(1L))
            assertTrue(SelectionTracker.isValidKey("valid"))
        }

        /**
         * 测试isSkipKey方法对跳过key的识别
         */
        @Test
        fun `isSkipKey should return true for skip keys`() {
            assertTrue(SelectionTracker.isSkipKey(-2))
            assertTrue(SelectionTracker.isSkipKey(-2L))
        }

        /**
         * 测试isSkipKey方法对非跳过key的识别
         */
        @Test
        fun `isSkipKey should return false for non-skip keys`() {
            assertFalse(SelectionTracker.isSkipKey(1))
            assertFalse(SelectionTracker.isSkipKey(1L))
            assertFalse(SelectionTracker.isSkipKey("not-skip"))
        }
    }

    /**
     * 测试RecycleSelectionBuilder类的功能
     */
    inner class RecycleSelectionBuilderTests {
        private lateinit var builder: RecycleSelectionBuilder<String>
        private val mockRecyclerView = mockk<RecyclerView>(relaxed = true)
        private val mockSelectionDelegate = mockk<SelectionDelegate<String>>(relaxed = true)
        private val mockKeyProvider = mockk<ItemKeyProvider<String>>(relaxed = true)
        private val mockDetailsLookup = mockk<ItemDetailsLookup<String>>(relaxed = true)
        private val mockAdapter = mockk<RecyclerView.Adapter<*>>(relaxed = true)

        /**
         * 在每个测试方法执行前初始化builder和mock对象
         */
        @Before
        fun setup() {
            every { mockRecyclerView.adapter } returns mockAdapter
            builder = RecycleSelectionBuilder(
                "test",
                mockRecyclerView,
                mockSelectionDelegate,
                mockKeyProvider,
                mockDetailsLookup
            )
        }

        /**
         * 测试withSelectMode方法设置多选模式的功能
         */
        @Test
        fun `withSelectMode should set multiple selection mode`() {
            builder.withSelectMode(false)
            val tracker = builder.build()
            assertFalse(tracker.canMultipleSelect())
        }

        /**
         * 测试withSelectionPredicates方法设置选择条件的功能
         */
        @Test
        fun `withSelectionPredicates should set selection predicate`() {
            val predicate = SelectionPredicates.createSingleSelect<String>()
            builder.withSelectionPredicates(predicate)
            val tracker = builder.build()
            assertFalse(tracker.canMultipleSelect())
        }

        /**
         * 测试withScrollThresholdRatio方法设置有效滚动阈值比例的功能
         */
        @Test
        fun `withScrollThresholdRatio should set valid ratio`() {
            builder.withScrollThresholdRatio(0.5f)
            val tracker = builder.build()
            assertEquals(0.5f, tracker.scrollThresholdRatio)
        }

        /**
         * 测试withScrollThresholdRatio方法忽略无效滚动阈值比例的功能
         */
        @Test
        fun `withScrollThresholdRatio should ignore invalid ratio`() {
            builder.withScrollThresholdRatio(-0.1f)
            val tracker = builder.build()
            assertNotEquals(-0.1f, tracker.scrollThresholdRatio)
            assertEquals(AutoScroller.DEFAULT_SCROLL_THRESHOLD_RATIO, tracker.scrollThresholdRatio)
        }

        /**
         * 测试build方法注册适配器数据观察者的功能
         */
        @Test
        fun `build should register adapter data observer`() {
            builder.build()
            verify(exactly = 1) { mockAdapter.registerAdapterDataObserver(any()) }
        }

        /**
         * 测试build方法添加项目触摸监听器的功能
         */
        @Test
        fun `build should add item touch listener`() {
            builder.build()
            verify(exactly = 1) { mockRecyclerView.addOnItemTouchListener(any()) }
        }
    }

    /**
     * 测试LAYOUT_TYPE枚举的值是否正确
     */
    @Test
    fun `LAYOUT_TYPE enum should have correct values`() {
        assertEquals(0, SelectionTracker.LAYOUT_TYPE.LIST.ordinal)
        assertEquals(1, SelectionTracker.LAYOUT_TYPE.GRID.ordinal)
    }
}