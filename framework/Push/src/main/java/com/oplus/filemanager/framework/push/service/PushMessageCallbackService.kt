/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: PushMessageCallbackService.kt
 ** Description: 透传消息的回调Service
 ** Version: 1.0
 ** Date : 2025/04/09
 ** Author: chao.xue
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 *  chao.xue 2025/04/09   1.0    create
 ****************************************************************/
package com.oplus.filemanager.framework.push.service

import android.content.Context
import com.filemanager.common.riskctrl.StrategyConfig
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.google.gson.Gson
import com.google.gson.JsonParseException
import com.heytap.msp.push.mode.DataMessage
import com.heytap.msp.push.service.DataMessageCallbackService
import com.oplus.filemanager.interfaze.ad.IAdvertApi

class PushMessageCallbackService : DataMessageCallbackService() {

    companion object {
        private const val TAG = "PushMessageCallbackService"
    }

    override fun onCreate() {
        super.onCreate()
        Log.e(TAG, "onCreate")
    }

    override fun processMessage(context: Context?, message: DataMessage?) {
        super.processMessage(context, message)
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "processMessage start, msg:$message")
        dispatch(context, message)
        Log.d(TAG, "processMessage end, cost: ${System.currentTimeMillis() - startTime} ms")
    }

    private fun dispatch(context: Context?, message: DataMessage?) {
        if (message == null) {
            Log.w(TAG, "dispatch -> message is null")
            return
        }
        val content = message.content
        if (isAdStrategyJson(content)) {
            Log.w(TAG, "dispatch -> is advert strategy message")
            val advertApi = Injector.injectFactory<IAdvertApi>()
            advertApi?.processAdStrategy(content)
        }
    }

    /**
     * 消息中包含strategyId字段就认为是风险控制策略消息
     */
    private fun isAdStrategyJson(msg: String?): Boolean {
        try {
            val config = Gson().fromJson<StrategyConfig>(msg, StrategyConfig::class.java)
            return config.strategyId.isNotEmpty()
        } catch (e: JsonParseException) {
            Log.w(TAG, "isAdStrategyJson error", e)
        }
        return false
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.e(TAG, "onDestroy")
    }
}