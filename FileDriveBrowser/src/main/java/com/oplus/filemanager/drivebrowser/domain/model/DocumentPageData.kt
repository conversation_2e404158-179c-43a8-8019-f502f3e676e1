/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentPageData
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/14 19:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.domain.model

data class DocumentPageData(
    var lists: List<CloudDocumentItem> = emptyList(),
    var nextPageOffset: Int = 0,
    var isLastPage: Boolean = false
)