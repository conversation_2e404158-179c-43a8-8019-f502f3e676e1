/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: SendTask.kt
 ** Description: real send method
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<PERSON><PERSON><PERSON>@oppo.com)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.share

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_CLEAR_TASK
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import android.graphics.Rect
import android.net.Uri
import android.os.TransactionTooLargeException
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants.IS_FOLDER
import com.filemanager.common.constants.CommonConstants.MAX_SEND_COUNT
import com.filemanager.common.fileutils.HiddenFileHelper.isHiddenFile
import com.filemanager.common.fileutils.HiddenFileHelper.isNeedShowHiddenFile
import com.filemanager.common.fileutils.UriHelper.getFileUri
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.base.*
import com.filemanager.fileoperate.open.OpenFileFactory
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import java.lang.ref.WeakReference

class SendTask(val mContext: WeakReference<Context>, private val mFileAction: BaseFileAction<out BaseFileActionObserver>, private val mIsCloudDriver: Boolean) {
    companion object {
        private const val TAG = "SendTask"
        private const val SHOW_PROGRESS_MSG_TIMEOUT: Long = 300L
    }

    private val mUris = ArrayList<Uri>()
    private val mFileLists = ArrayList<BaseFileBean>()
    private var mIsDirectory = false
    private var mIsDrmFile = false
    private var mMimeType: String? = null
    private var mRect: Rect? = null

    fun send(files: ArrayList<out BaseFileBean>, rect: Rect?) {
        mRect = rect
        onExecute(innerExecute(files), files)
    }

    private fun innerExecute(params: ArrayList<out BaseFileBean>): Boolean {
        mFileAction.notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                appContext.getString(com.filemanager.common.R.string.string_being_calculated), true
            ), SHOW_PROGRESS_MSG_TIMEOUT)
        for (file in params) {
            if (mFileAction.isCancelled()) {
                return false
            }
            if (mUris.size > MAX_SEND_COUNT) {
                Log.d(TAG, "innerExecute: Reached the limit number, out of the loop")
                break
            }
            if (!JavaFileHelper.exists(file)) {
                continue
            }
            var type = file.mLocalType
            if (type == MimeTypeHelper.DRM_TYPE) {
                mIsDrmFile = true
                return false
            }
            if (type == MimeTypeHelper.DIRECTORY_TYPE) {
                mIsDirectory = true
                val isShowHideFile = isNeedShowHiddenFile()
                traversalFile(file, isShowHideFile)
                continue
            } else {
                if (type == MimeTypeHelper.UNKNOWN_TYPE) {
                    type = MimeTypeHelper.getMediaType(file.mData)
                }
                addMimeType(file)
            }
            /** bug：8375503，8790323，兼容微信，分享media provider uri的视频文件到微信会出现发送中断的情况（文档压缩包等文件分享到微信分身会不生效），Jerry结论应用自己整改，分享优先用file provider的uri
            (分享面板显示缩图不支持file provider uri，图片优先还是用媒体库uri)
             **/
            val fileUri = getFileUri(file, fileType = type, mediaFirstParam = (type == MimeTypeHelper.IMAGE_TYPE)) ?: file.mLocalFileUri
            if (fileUri != null) {
                mFileLists.add(file)
                mUris.add(fileUri)
            }
        }
        Log.v(TAG, "size =" + mUris.size)
        return !(mUris.size <= 0 || mIsDrmFile)
    }

    private fun onExecute(result: Boolean, files: ArrayList<out BaseFileBean>) {
        if (mFileAction.isCancelled()) {
            return
        }
        if (result) {
            if (mUris.size > MAX_SEND_COUNT) {
                Log.w(TAG, "onExecute: mUris.size > MAX_SEND_COUNT")
                mFileAction.notifyObserver(ACTION_FAILED, TOO_MANY_FILES_STATE)
            } else {
                if (mIsCloudDriver) {
                    mFileLists.forEach { file ->
                        StatisticsUtils.onCommon(
                            mContext.get(),
                            StatisticsUtils.EVENT_UPLOAD_TO_CLOUD,
                            mapOf(StatisticsUtils.FILE_TYPE to FileTypeUtils.getExtension(file.mData))
                        )
                    }
                    mFileLists.clear()
                    mContext.get()?.let { Injector.injectFactory<ICloudDrive>()?.uploadFiles(it, mUris) }
                } else {
                    share()
                }
                mFileAction.notifyObserver(ACTION_DONE)
            }
        } else {
            if (files.isNotEmpty() && KtUtils.checkDfmFileAndDfmDisconnected(files[0].mData)) {
                mFileAction.notifyObserver(ACTION_FAILED, SEND_ERROR_DFM_DISCONNECTED)
            } else if (mIsDrmFile) {
                mFileAction.notifyObserver(ACTION_FAILED, CAN_NOT_SEND_DRM_FILES)
            } else {
                mFileAction.notifyObserver(ACTION_FAILED, EMPTY_FOLDER_STATE)
            }
        }
    }

    private fun share() {
        val intent = Intent()
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)

        if (!FeatureCompat.isSmallScreenPhone) {
            intent.putExtra("start_chooser_anchor_x", mRect?.centerX())
            intent.putExtra("start_chooser_anchor_y", mRect?.top)
            intent.putExtra("start_chooser_anchor_width", mRect?.width())
            intent.putExtra("start_chooser_anchor_height", mRect?.height())
        }

        if (mUris.size == 1) {
            intent.action = Intent.ACTION_SEND
            intent.putExtra(Intent.EXTRA_STREAM, mUris[0])
        } else {
            intent.action = Intent.ACTION_SEND_MULTIPLE
            intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, mUris)
        }
        intent.putExtra(FileActionShare.SEND_ENTRANCE, appContext.packageName)
        if (mIsDirectory) {
            intent.putExtra("send_folder", true)
            intent.putExtra(IS_FOLDER, true)
        }
        intent.type = mMimeType

        try {
            val context = mContext.get()
            intent.addFlags(FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(FLAG_ACTIVITY_CLEAR_TASK)
            val excludeComponents = OpenFileFactory.getExcludeComponent(appContext, intent)
            context?.startActivity(
                Intent.createChooser(
                    intent,
                    appContext.getText(com.filemanager.common.R.string.doc_viewer_share_menu_title)
                ).apply {
                    putExtra(Intent.EXTRA_EXCLUDE_COMPONENTS, excludeComponents)
                }
            )
        } catch (ex: ActivityNotFoundException) {
            mFileAction.notifyObserver(ACTION_FAILED, SEND_ERROR)
        } catch (throwable: Throwable) {
            val causeThrowable: Throwable? = throwable.cause
            if (causeThrowable is TransactionTooLargeException) {
                mFileAction.notifyObserver(ACTION_FAILED, TOO_LARGE)
                return
            } else if (mFileLists.isNotEmpty() && KtUtils.checkDfmFileAndDfmDisconnected(mFileLists[0].mData)) {
                mFileAction.notifyObserver(ACTION_FAILED, SEND_ERROR_DFM_DISCONNECTED)
                return
            } else {
                mFileAction.notifyObserver(ACTION_FAILED, SEND_ERROR)
                return
            }
        }
    }

    private fun traversalFile(file: BaseFileBean, isShowHideFile: Boolean = false) {
        if (!isShowHideFile && isHiddenFile(file.mDisplayName)) {
            //If hidden folder are not displayed, filter the hidden folder and the folder's files
            return
        }
        val files = JavaFileHelper.listFileBeans(file, excludeHideFile = !isShowHideFile) ?: return
        for (i in files.indices) {
            if (mUris.size > MAX_SEND_COUNT) {
                Log.d(TAG, "traversalFile: Reached the limit number, out of the loop")
                return
            }
            val tempFile = files[i]
            if (!isShowHideFile && isHiddenFile(tempFile.mDisplayName)) {
                //If hidden files are not displayed, filter the hidden files
                continue
            }
            if (!JavaFileHelper.exists(tempFile)) {
                continue
            }
            val type = tempFile.mLocalType
            if (type == MimeTypeHelper.DRM_TYPE) {
                mIsDrmFile = true
                return
            }
            if (type == MimeTypeHelper.DIRECTORY_TYPE) {
                traversalFile(tempFile, isShowHideFile)
            } else {
                addMimeType(tempFile)
                getFileUri(tempFile, fileType = type)?.let { mUris.add(it) }
            }
        }
    }

    private fun addMimeType(file: BaseFileBean) {
        val allMimeType = "*/*"
        if (allMimeType.equals(mMimeType, false)) {
            return
        }
        val mimeType = MimeTypeHelper.getMimeTypeFromPath(file.mData)
        if (mimeType == null) {
            mMimeType = allMimeType
            return
        }
        if ((allMimeType == mimeType) || (mMimeType == null)) {
            mMimeType = mimeType
            return
        }
        Log.v("SendTask", "mMimeType=$mMimeType mimeType=$mimeType")
        val mimeTypes1 = mMimeType!!.split("/").toTypedArray()
        val mimeTypes2 = mimeType.split("/").toTypedArray()
        if ((mimeTypes1.size != 2) || (mimeTypes2.size != 2) || (mimeTypes1[0] != mimeTypes2[0])) {
            mMimeType = allMimeType
        } else if (mimeTypes1[1] != mimeTypes2[1]) {
            mMimeType = "${mimeTypes1[0]}/*"
        }
    }
}