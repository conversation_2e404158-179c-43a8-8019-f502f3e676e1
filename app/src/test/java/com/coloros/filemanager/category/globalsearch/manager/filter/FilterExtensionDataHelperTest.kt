/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilterExtensionDataHelperTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/29       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.category.globalsearch.manager.filter

import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterExtensionDataHelper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class FilterExtensionDataHelperTest {

    private lateinit var directoryFileBean: BaseFileBean
    private lateinit var nullDisplayNameFileBean: BaseFileBean
    private lateinit var emptyDisplayNameFileBean: BaseFileBean
    private lateinit var firstFileBean: BaseFileBean
    private lateinit var secondFileBean: BaseFileBean
    private lateinit var thirdFileBean: BaseFileBean
    private lateinit var fourthFileBean: BaseFileBean
    private lateinit var fifthFileBean: BaseFileBean

    @Before
    fun setUp() {
        directoryFileBean = BaseFileBean().apply {
            mIsDirectory = true
        }
        nullDisplayNameFileBean = BaseFileBean().apply {
            mIsDirectory = false
            mDisplayName = null
        }
        emptyDisplayNameFileBean = BaseFileBean().apply {
            mIsDirectory = false
            mDisplayName = ""
        }
        firstFileBean = BaseFileBean().apply {
            mIsDirectory = false
        }
        secondFileBean = BaseFileBean().apply {
            mIsDirectory = false
        }
        thirdFileBean = BaseFileBean().apply {
            mIsDirectory = false
        }
        fourthFileBean = BaseFileBean().apply {
            mIsDirectory = false
        }
        fifthFileBean = BaseFileBean().apply {
            mIsDirectory = false
        }
    }

    @Test
    fun testFilterImgData() {
        val imageFilterItem = FilterItem(
            FilterConstants.FILTER_IMG_JPG,
            FilterCondition(FilterConstants.FILTER_IMG, ""),
            ""
        )
        val filterExtensionDataHelper = FilterExtensionDataHelper()
        firstFileBean.mDisplayName = "image.jpg"
        secondFileBean.mDisplayName = "image.jpeg"
        thirdFileBean.mDisplayName = "image.png"
        fourthFileBean.mDisplayName = "image.gif"
        fifthFileBean.mDisplayName = "image.bmp"
        val heifFileBean = BaseFileBean().apply {
            mIsDirectory = false
            mDisplayName = "image.heif"
        }
        val dataList = mutableListOf<BaseFileBean>().apply {
            add(directoryFileBean)
            add(nullDisplayNameFileBean)
            add(emptyDisplayNameFileBean)
            add(firstFileBean)
            add(secondFileBean)
            add(thirdFileBean)
            add(fourthFileBean)
            add(fifthFileBean)
            add(heifFileBean)
        }
        // filter jpg img.
        val jpgResult = mutableListOf<BaseFileBean>().apply {
            add(firstFileBean)
            add(secondFileBean)
        }
        Assert.assertEquals(jpgResult, filterExtensionDataHelper.filterData(imageFilterItem, dataList))

        // filter png img.
        imageFilterItem.id = FilterConstants.FILTER_IMG_PNG
        val pngResult = mutableListOf<BaseFileBean>().apply {
            add(thirdFileBean)
        }
        Assert.assertEquals(pngResult, filterExtensionDataHelper.filterData(imageFilterItem, dataList))

        // filter gig img.
        imageFilterItem.id = FilterConstants.FILTER_IMG_GIF
        val gifResult = mutableListOf<BaseFileBean>().apply {
            add(fourthFileBean)
        }
        Assert.assertEquals(gifResult, filterExtensionDataHelper.filterData(imageFilterItem, dataList))

        // filter bmp img.
        imageFilterItem.id = FilterConstants.FILTER_IMG_BMP
        val bmpResult = mutableListOf<BaseFileBean>().apply {
            add(fifthFileBean)
        }
        Assert.assertEquals(bmpResult, filterExtensionDataHelper.filterData(imageFilterItem, dataList))

        // filter heif img.
        imageFilterItem.id = FilterConstants.FILTER_IMG_HEIF
        val heifResult = mutableListOf<BaseFileBean>().apply {
            add(heifFileBean)
        }
        Assert.assertEquals(heifResult, filterExtensionDataHelper.filterData(imageFilterItem, dataList))
    }

    @Test
    fun testFilterVideoData() {
        val videoFilterItem = FilterItem(
            FilterConstants.FILTER_VIDEO_MP4,
            FilterCondition(FilterConstants.FILTER_VIDEO, ""),
            ""
        )
        val filterExtensionDataHelper = FilterExtensionDataHelper()
        firstFileBean.mDisplayName = "video.mp4"
        secondFileBean.mDisplayName = "video.mkv"
        thirdFileBean.mDisplayName = "video.avi"
        fourthFileBean.mDisplayName = "video.wmv"
        fifthFileBean.mDisplayName = "video.3gp"
        val dataList = mutableListOf<BaseFileBean>().apply {
            add(directoryFileBean)
            add(nullDisplayNameFileBean)
            add(emptyDisplayNameFileBean)
            add(firstFileBean)
            add(secondFileBean)
            add(thirdFileBean)
            add(fourthFileBean)
            add(fifthFileBean)
        }
        // filter mp4 video.
        val mp4Result = mutableListOf<BaseFileBean>().apply {
            add(firstFileBean)
        }
        Assert.assertEquals(mp4Result, filterExtensionDataHelper.filterData(videoFilterItem, dataList))

        // filter mkv video.
        videoFilterItem.id = FilterConstants.FILTER_VIDEO_MKV
        val mkvResult = mutableListOf<BaseFileBean>().apply {
            add(secondFileBean)
        }
        Assert.assertEquals(mkvResult, filterExtensionDataHelper.filterData(videoFilterItem, dataList))

        // filter avi video.
        videoFilterItem.id = FilterConstants.FILTER_VIDEO_AVI
        val aviResult = mutableListOf<BaseFileBean>().apply {
            add(thirdFileBean)
        }
        Assert.assertEquals(aviResult, filterExtensionDataHelper.filterData(videoFilterItem, dataList))

        // filter gig video.
        videoFilterItem.id = FilterConstants.FILTER_VIDEO_WMV
        val wmvResult = mutableListOf<BaseFileBean>().apply {
            add(fourthFileBean)
        }
        Assert.assertEquals(wmvResult, filterExtensionDataHelper.filterData(videoFilterItem, dataList))

        // filter 3gp video.
        videoFilterItem.id = FilterConstants.FILTER_VIDEO_3GP
        val result3gp = mutableListOf<BaseFileBean>().apply {
            add(fifthFileBean)
        }
        Assert.assertEquals(result3gp, filterExtensionDataHelper.filterData(videoFilterItem, dataList))
    }

    @Test
    fun testFilterAudioData() {
        val audioFilterItem = FilterItem(
            FilterConstants.FILTER_AUDIO_MP3,
            FilterCondition(FilterConstants.FILTER_AUDIO, ""),
            ""
        )
        val filterExtensionDataHelper = FilterExtensionDataHelper()
        firstFileBean.mDisplayName = "audio.mp3"
        secondFileBean.mDisplayName = "audio.m4a"
        thirdFileBean.mDisplayName = "audio.amr"
        fourthFileBean.mDisplayName = "audio.ogg"
        fifthFileBean.mDisplayName = "audio.aac"
        val dataList = mutableListOf<BaseFileBean>().apply {
            add(directoryFileBean)
            add(nullDisplayNameFileBean)
            add(emptyDisplayNameFileBean)
            add(firstFileBean)
            add(secondFileBean)
            add(thirdFileBean)
            add(fourthFileBean)
            add(fifthFileBean)
        }
        // filter mp3 audio.
        val mp3Result = mutableListOf<BaseFileBean>().apply {
            add(firstFileBean)
        }
        Assert.assertEquals(mp3Result, filterExtensionDataHelper.filterData(audioFilterItem, dataList))

        // filter m4a audio.
        audioFilterItem.id = FilterConstants.FILTER_AUDIO_M4A
        val m4aResult = mutableListOf<BaseFileBean>().apply {
            add(secondFileBean)
        }
        Assert.assertEquals(m4aResult, filterExtensionDataHelper.filterData(audioFilterItem, dataList))

        // filter amr audio.
        audioFilterItem.id = FilterConstants.FILTER_AUDIO_AMR
        val amrResult = mutableListOf<BaseFileBean>().apply {
            add(thirdFileBean)
        }
        Assert.assertEquals(amrResult, filterExtensionDataHelper.filterData(audioFilterItem, dataList))

        // filter ogg audio.
        audioFilterItem.id = FilterConstants.FILTER_AUDIO_OGG
        val oggResult = mutableListOf<BaseFileBean>().apply {
            add(fourthFileBean)
        }
        Assert.assertEquals(oggResult, filterExtensionDataHelper.filterData(audioFilterItem, dataList))

        // filter aac audio.
        audioFilterItem.id = FilterConstants.FILTER_AUDIO_AAC
        val aacResult = mutableListOf<BaseFileBean>().apply {
            add(fifthFileBean)
        }
        Assert.assertEquals(aacResult, filterExtensionDataHelper.filterData(audioFilterItem, dataList))
    }

    @Test
    fun testFilterDocData() {
        val docFilterItem = FilterItem(
            FilterConstants.FILTER_DOC_PDF,
            FilterCondition(FilterConstants.FILTER_DOC, ""),
            ""
        )
        val filterExtensionDataHelper = FilterExtensionDataHelper()
        firstFileBean.mDisplayName = "doc.pdf"
        secondFileBean.mDisplayName = "doc.xls"
        thirdFileBean.mDisplayName = "doc.doc"
        fourthFileBean.mDisplayName = "doc.ppt"
        fifthFileBean.mDisplayName = "doc.txt"
        val xlsxFileBean = BaseFileBean().apply {
            mIsDirectory = false
            mDisplayName = "doc.xlsx"
        }
        val docxFileBean = BaseFileBean().apply {
            mIsDirectory = false
            mDisplayName = "doc.docx"
        }
        val pptxFileBean = BaseFileBean().apply {
            mIsDirectory = false
            mDisplayName = "doc.pptx"
        }
        val dataList = initDocDataList()
        dataList.add(xlsxFileBean)
        dataList.add(docxFileBean)
        dataList.add(pptxFileBean)
        // filter pdf doc.
        val pdfResult = mutableListOf<BaseFileBean>().apply {
            add(firstFileBean)
        }
        Assert.assertEquals(pdfResult, filterExtensionDataHelper.filterData(docFilterItem, dataList))

        // filter xls doc.
        docFilterItem.id = FilterConstants.FILTER_DOC_XLS
        val xlsResult = mutableListOf<BaseFileBean>().apply {
            add(secondFileBean)
            add(xlsxFileBean)
        }
        Assert.assertEquals(xlsResult, filterExtensionDataHelper.filterData(docFilterItem, dataList))

        // filter doc doc.
        docFilterItem.id = FilterConstants.FILTER_DOC_DOC
        val docResult = mutableListOf<BaseFileBean>().apply {
            add(thirdFileBean)
            add(docxFileBean)
        }
        Assert.assertEquals(docResult, filterExtensionDataHelper.filterData(docFilterItem, dataList))

        // filter ppt doc.
        docFilterItem.id = FilterConstants.FILTER_DOC_PPT
        val pptResult = mutableListOf<BaseFileBean>().apply {
            add(fourthFileBean)
            add(pptxFileBean)
        }
        Assert.assertEquals(pptResult, filterExtensionDataHelper.filterData(docFilterItem, dataList))

        // filter txt doc.
        docFilterItem.id = FilterConstants.FILTER_DOC_TXT
        val txtResult = mutableListOf<BaseFileBean>().apply {
            add(fifthFileBean)
        }
        Assert.assertEquals(txtResult, filterExtensionDataHelper.filterData(docFilterItem, dataList))
    }

    private fun initDocDataList(): MutableList<BaseFileBean> {
        return mutableListOf<BaseFileBean>().apply {
            add(directoryFileBean)
            add(nullDisplayNameFileBean)
            add(emptyDisplayNameFileBean)
            add(firstFileBean)
            add(secondFileBean)
            add(thirdFileBean)
            add(fourthFileBean)
            add(fifthFileBean)
        }
    }

    @Test
    fun testFilterCompressData() {
        val compressFilterItem = FilterItem(
            FilterConstants.FILTER_COMPRESS_RAR,
            FilterCondition(FilterConstants.FILTER_COMPRESS, ""),
            ""
        )
        val filterExtensionDataHelper = FilterExtensionDataHelper()
        firstFileBean.mDisplayName = "compress.rar"
        secondFileBean.mDisplayName = "compress.zip"
        thirdFileBean.mDisplayName = "compress.jar"
        fourthFileBean.mDisplayName = "compress.7z"
        val dataList = mutableListOf<BaseFileBean>().apply {
            add(directoryFileBean)
            add(nullDisplayNameFileBean)
            add(emptyDisplayNameFileBean)
            add(firstFileBean)
            add(secondFileBean)
            add(thirdFileBean)
            add(fourthFileBean)
        }
        // filter rar compress.
        val rarResult = mutableListOf<BaseFileBean>().apply {
            add(firstFileBean)
        }
        Assert.assertEquals(rarResult, filterExtensionDataHelper.filterData(compressFilterItem, dataList))

        // filter zip compress.
        compressFilterItem.id = FilterConstants.FILTER_COMPRESS_ZIP
        val zipResult = mutableListOf<BaseFileBean>().apply {
            add(secondFileBean)
        }
        Assert.assertEquals(zipResult, filterExtensionDataHelper.filterData(compressFilterItem, dataList))

        // filter jar compress.
        compressFilterItem.id = FilterConstants.FILTER_COMPRESS_JAR
        val jarResult = mutableListOf<BaseFileBean>().apply {
            add(thirdFileBean)
        }
        Assert.assertEquals(jarResult, filterExtensionDataHelper.filterData(compressFilterItem, dataList))

        compressFilterItem.id = FilterConstants.FILTER_COMPRESS_7ZIP
        val p7zResult = mutableListOf<BaseFileBean>().apply {
            add(fourthFileBean)
        }
        Assert.assertEquals(p7zResult, filterExtensionDataHelper.filterData(compressFilterItem, dataList))
    }
}