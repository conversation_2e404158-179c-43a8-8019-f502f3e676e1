#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 230686720 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3550), pid=25352, tid=17640
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\CategoryAlbumSet\build\20250901_18108630666102982445.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 12:11:47 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 8.804870 seconds (0d 0h 0m 8s)

---------------  T H R E A D  ---------------

Current thread (0x000001dab3b65a00):  VMThread "VM Thread" [stack: 0x0000003baa200000,0x0000003baa300000] [id=17640]

Stack: [0x0000003baa200000,0x0000003baa300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x674bb9]
V  [jvm.dll+0x6694f2]
V  [jvm.dll+0x3031d6]
V  [jvm.dll+0x30a756]
V  [jvm.dll+0x359f9e]
V  [jvm.dll+0x35a1cf]
V  [jvm.dll+0x2da3e8]
V  [jvm.dll+0x2d87f5]
V  [jvm.dll+0x2d7dfc]
V  [jvm.dll+0x31b4cb]
V  [jvm.dll+0x7df26b]
V  [jvm.dll+0x7dffa4]
V  [jvm.dll+0x7e04bd]
V  [jvm.dll+0x7e0894]
V  [jvm.dll+0x7e0960]
V  [jvm.dll+0x788bba]
V  [jvm.dll+0x676b35]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x0000003ba9cfb370): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001da875d23f0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001dafbf8e450, length=16, elements={
0x000001da875d23f0, 0x000001dab3b6f610, 0x000001dab3b70490, 0x000001dab3b8bf20,
0x000001dab3b8e8e0, 0x000001dab3b8f290, 0x000001dab3b8fc40, 0x000001dab3b94230,
0x000001dab3b94cd0, 0x000001dab3ba8ea0, 0x000001dab45f39c0, 0x000001dab45f3ed0,
0x000001dab4610a60, 0x000001dab4624f80, 0x000001dafaaab080, 0x000001dafbc52e80
}

Java Threads: ( => current thread )
  0x000001da875d23f0 JavaThread "main" [_thread_blocked, id=16252, stack(0x0000003ba9c00000,0x0000003ba9d00000)]
  0x000001dab3b6f610 JavaThread "Reference Handler" daemon [_thread_blocked, id=3444, stack(0x0000003baa300000,0x0000003baa400000)]
  0x000001dab3b70490 JavaThread "Finalizer" daemon [_thread_blocked, id=9972, stack(0x0000003baa400000,0x0000003baa500000)]
  0x000001dab3b8bf20 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=24708, stack(0x0000003baa500000,0x0000003baa600000)]
  0x000001dab3b8e8e0 JavaThread "Attach Listener" daemon [_thread_blocked, id=13636, stack(0x0000003baa600000,0x0000003baa700000)]
  0x000001dab3b8f290 JavaThread "Service Thread" daemon [_thread_blocked, id=6292, stack(0x0000003baa700000,0x0000003baa800000)]
  0x000001dab3b8fc40 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=15544, stack(0x0000003baa800000,0x0000003baa900000)]
  0x000001dab3b94230 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=25544, stack(0x0000003baa900000,0x0000003baaa00000)]
  0x000001dab3b94cd0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=20636, stack(0x0000003baaa00000,0x0000003baab00000)]
  0x000001dab3ba8ea0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=19072, stack(0x0000003baab00000,0x0000003baac00000)]
  0x000001dab45f39c0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=27464, stack(0x0000003baac00000,0x0000003baad00000)]
  0x000001dab45f3ed0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=29620, stack(0x0000003baad00000,0x0000003baae00000)]
  0x000001dab4610a60 JavaThread "Notification Thread" daemon [_thread_blocked, id=11808, stack(0x0000003baae00000,0x0000003baaf00000)]
  0x000001dab4624f80 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=23184, stack(0x0000003bab000000,0x0000003bab100000)]
  0x000001dafaaab080 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=24048, stack(0x0000003babe00000,0x0000003babf00000)]
  0x000001dafbc52e80 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=17052, stack(0x0000003babf00000,0x0000003bac000000)]

Other Threads:
=>0x000001dab3b65a00 VMThread "VM Thread" [stack: 0x0000003baa200000,0x0000003baa300000] [id=17640]
  0x000001dab4619f00 WatcherThread [stack: 0x0000003baaf00000,0x0000003bab000000] [id=22208]
  0x000001da87681400 GCTaskThread "GC Thread#0" [stack: 0x0000003ba9d00000,0x0000003ba9e00000] [id=17812]
  0x000001dafa2c3050 GCTaskThread "GC Thread#1" [stack: 0x0000003bab300000,0x0000003bab400000] [id=7352]
  0x000001dafa2c3300 GCTaskThread "GC Thread#2" [stack: 0x0000003bab400000,0x0000003bab500000] [id=12744]
  0x000001dafa2c35b0 GCTaskThread "GC Thread#3" [stack: 0x0000003bab500000,0x0000003bab600000] [id=2448]
  0x000001dafa2c3860 GCTaskThread "GC Thread#4" [stack: 0x0000003bab600000,0x0000003bab700000] [id=19172]
  0x000001dafa2c3b10 GCTaskThread "GC Thread#5" [stack: 0x0000003bab700000,0x0000003bab800000] [id=26352]
  0x000001dafa2c3dc0 GCTaskThread "GC Thread#6" [stack: 0x0000003bab800000,0x0000003bab900000] [id=13716]
  0x000001dafa2c4070 GCTaskThread "GC Thread#7" [stack: 0x0000003bab900000,0x0000003baba00000] [id=20004]
  0x000001dafa2c4320 GCTaskThread "GC Thread#8" [stack: 0x0000003baba00000,0x0000003babb00000] [id=1716]
  0x000001dafa2c45d0 GCTaskThread "GC Thread#9" [stack: 0x0000003babb00000,0x0000003babc00000] [id=22052]
  0x000001dafa305e20 GCTaskThread "GC Thread#10" [stack: 0x0000003babc00000,0x0000003babd00000] [id=26656]
  0x000001dafa306380 GCTaskThread "GC Thread#11" [stack: 0x0000003babd00000,0x0000003babe00000] [id=13604]
  0x000001dafbc6b320 GCTaskThread "GC Thread#12" [stack: 0x0000003bac100000,0x0000003bac200000] [id=22824]
  0x000001da87692100 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000003ba9e00000,0x0000003ba9f00000] [id=24664]
  0x000001da876932b0 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000003ba9f00000,0x0000003baa000000] [id=25624]
  0x000001dafa306630 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000003bab100000,0x0000003bab200000] [id=17388]
  0x000001dafa306e40 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000003bab200000,0x0000003bab300000] [id=24736]
  0x000001dab3a9e6e0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000003baa000000,0x0000003baa100000] [id=17576]
  0x000001dafb8f5920 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000003bac200000,0x0000003bac300000] [id=26504]
  0x000001dab3a9f010 ConcurrentGCThread "G1 Service" [stack: 0x0000003baa100000,0x0000003baa200000] [id=24428]

Threads with active compile tasks:
C2 CompilerThread0     8894 3886   !   4       jdk.internal.loader.BuiltinClassLoader::findClassOnClassPathOrNull (64 bytes)
C1 CompilerThread0     8894 3924       1       gnu.trove.THash::size (5 bytes)
C1 CompilerThread1     8894 3929       1       org.jetbrains.kotlin.resolve.ImportPath::getAlias (5 bytes)
C1 CompilerThread2     8894 3931       3       org.jetbrains.kotlin.resolve.QualifiedExpressionResolver$QualifierPart::<init> (32 bytes)
C1 CompilerThread3     8894 3930       3       org.jetbrains.kotlin.config.AnalysisFlag::hashCode (8 bytes)
C2 CompilerThread1     8894 3898       4       java.lang.Class::reflectionData (43 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001da875cf260] Threads_lock - owner thread: 0x000001dab3b65a00
[0x000001da875ce960] Heap_lock - owner thread: 0x000001da875d23f0

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001dab5000000-0x000001dab5bd0000-0x000001dab5bd0000), size 12386304, SharedBaseAddress: 0x000001dab5000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001dab6000000-0x000001daf6000000, reserved size: 1073741824
Narrow klass base: 0x000001dab5000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 69632K, used 27948K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 36888K, committed 37120K, reserved 1114112K
  class space    used 4488K, committed 4608K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604c00000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604f4b200, 0x0000000605000000| 82%| O|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605800000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605c00000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000605dc9200, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607800000, 0x0000000607800000|100%| S|CS|TAMS 0x0000000607400000, 0x0000000607400000| Complete 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
| 113|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 126|0x0000000624000000, 0x0000000624000000, 0x0000000624400000|  0%| F|  |TAMS 0x0000000624000000, 0x0000000624000000| Untracked 

Card table byte_map: [0x000001da9f6e0000,0x000001daa06c0000] _byte_map_base: 0x000001da9c6bc000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001da87681a10, (CMBitMap*) 0x000001da87681a50
 Prev Bits: [0x000001daa16a0000, 0x000001daa9580000)
 Next Bits: [0x000001daa9580000, 0x000001dab1460000)

Polling page: 0x000001da85550000

Metaspace:

Usage:
  Non-class:     31.64 MB used.
      Class:      4.38 MB used.
       Both:     36.02 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      31.75 MB ( 50%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.50 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      36.25 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  14.73 MB
       Class:  11.52 MB
        Both:  26.25 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 54.94 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 168.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 578.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 1430.
num_chunk_merges: 6.
num_chunk_splits: 1175.
num_chunks_enlarged: 1054.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=2135Kb max_used=2135Kb free=117033Kb
 bounds [0x000001da96a20000, 0x000001da96c90000, 0x000001da9de80000]
CodeHeap 'profiled nmethods': size=119104Kb used=8610Kb max_used=8610Kb free=110493Kb
 bounds [0x000001da8ee80000, 0x000001da8f6f0000, 0x000001da962d0000]
CodeHeap 'non-nmethods': size=7488Kb used=2956Kb max_used=3031Kb free=4531Kb
 bounds [0x000001da962d0000, 0x000001da965e0000, 0x000001da96a20000]
 total_blobs=4506 nmethods=3924 adapters=491
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 8.551 Thread 0x000001dab45f39c0 nmethod 3920 0x000001da8f6dfa10 code [0x000001da8f6dfd60, 0x000001da8f6e0f28]
Event: 8.552 Thread 0x000001dab45f39c0 3917       3       java.util.Arrays::binarySearch (9 bytes)
Event: 8.552 Thread 0x000001dab3b94cd0 nmethod 3919 0x000001da8f6e1b90 code [0x000001da8f6e1f20, 0x000001da8f6e3db8]
Event: 8.553 Thread 0x000001dafaaab080 nmethod 3923 0x000001da8f6e7690 code [0x000001da8f6e7860, 0x000001da8f6e7b98]
Event: 8.553 Thread 0x000001dafaaab080 3927       3       java.util.ArrayList$SubList::size (9 bytes)
Event: 8.553 Thread 0x000001dab3b94cd0 3925       1       org.jetbrains.kotlin.resolve.lazy.FileScopeFactory$DefaultImportImpl::getImportContent (5 bytes)
Event: 8.553 Thread 0x000001dab45f3ed0 nmethod 3918 0x000001da8f6e4410 code [0x000001da8f6e4880, 0x000001da8f6e6df8]
Event: 8.553 Thread 0x000001dab45f39c0 nmethod 3917 0x000001da8f6e7d10 code [0x000001da8f6e7ea0, 0x000001da8f6e7fc8]
Event: 8.553 Thread 0x000001dab45f3ed0 3926       1       org.jetbrains.kotlin.psi.KtImportInfo$ImportContent$FqNameBased::getFqName (5 bytes)
Event: 8.553 Thread 0x000001dab3b94cd0 nmethod 3925 0x000001da96c35310 code [0x000001da96c354a0, 0x000001da96c35578]
Event: 8.553 Thread 0x000001dab45f39c0 3928       1       org.jetbrains.kotlin.config.AnalysisFlag::getDefaultValue (5 bytes)
Event: 8.553 Thread 0x000001dab3b94cd0 3932       3       java.util.ArrayList$SubList$1::hasNext (20 bytes)
Event: 8.553 Thread 0x000001dab45f3ed0 nmethod 3926 0x000001da96c35610 code [0x000001da96c357a0, 0x000001da96c35878]
Event: 8.553 Thread 0x000001dafaaab080 nmethod 3927 0x000001da8f6e8090 code [0x000001da8f6e8240, 0x000001da8f6e84c8]
Event: 8.553 Thread 0x000001dab45f39c0 nmethod 3928 0x000001da96c35910 code [0x000001da96c35aa0, 0x000001da96c35b78]
Event: 8.553 Thread 0x000001dab45f3ed0 3931       3       org.jetbrains.kotlin.resolve.QualifiedExpressionResolver$QualifierPart::<init> (32 bytes)
Event: 8.553 Thread 0x000001dafaaab080 3930       3       org.jetbrains.kotlin.config.AnalysisFlag::hashCode (8 bytes)
Event: 8.554 Thread 0x000001dab3b94cd0 nmethod 3932 0x000001da8f6e8610 code [0x000001da8f6e87a0, 0x000001da8f6e8918]
Event: 8.554 Thread 0x000001dab45f39c0 3929       1       org.jetbrains.kotlin.resolve.ImportPath::getAlias (5 bytes)
Event: 8.554 Thread 0x000001dab3b94cd0 3924       1       gnu.trove.THash::size (5 bytes)

GC Heap History (15 events):
Event: 0.788 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6843K, committed 6976K, reserved 1114112K
  class space    used 706K, committed 768K, reserved 1048576K
}
Event: 0.827 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 6965K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6843K, committed 6976K, reserved 1114112K
  class space    used 706K, committed 768K, reserved 1048576K
}
Event: 1.584 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 35637K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 15292K, committed 15488K, reserved 1114112K
  class space    used 2019K, committed 2112K, reserved 1048576K
}
Event: 1.586 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 9133K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 15292K, committed 15488K, reserved 1114112K
  class space    used 2019K, committed 2112K, reserved 1048576K
}
Event: 3.810 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 50093K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 1 survivors (4096K)
 Metaspace       used 21353K, committed 21504K, reserved 1114112K
  class space    used 2882K, committed 2944K, reserved 1048576K
}
Event: 3.813 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 520192K, used 15068K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 21353K, committed 21504K, reserved 1114112K
  class space    used 2882K, committed 2944K, reserved 1048576K
}
Event: 5.088 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 69632K, used 47836K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 2 survivors (8192K)
 Metaspace       used 27392K, committed 27584K, reserved 1114112K
  class space    used 3460K, committed 3520K, reserved 1048576K
}
Event: 5.094 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 18481K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 27392K, committed 27584K, reserved 1114112K
  class space    used 3460K, committed 3520K, reserved 1048576K
}
Event: 5.249 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 22577K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 28617K, committed 28800K, reserved 1114112K
  class space    used 3602K, committed 3648K, reserved 1048576K
}
Event: 5.257 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 18934K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 28617K, committed 28800K, reserved 1114112K
  class space    used 3602K, committed 3648K, reserved 1048576K
}
Event: 6.641 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 43510K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 33487K, committed 33728K, reserved 1114112K
  class space    used 4118K, committed 4224K, reserved 1048576K
}
Event: 6.648 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 69632K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 33487K, committed 33728K, reserved 1114112K
  class space    used 4118K, committed 4224K, reserved 1048576K
}
Event: 7.096 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 69632K, used 49152K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 34051K, committed 34240K, reserved 1114112K
  class space    used 4190K, committed 4288K, reserved 1048576K
}
Event: 7.107 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 69632K, used 24503K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 34051K, committed 34240K, reserved 1114112K
  class space    used 4190K, committed 4288K, reserved 1048576K
}
Event: 8.556 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total 69632K, used 44983K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 1 survivors (4096K)
 Metaspace       used 36888K, committed 37120K, reserved 1114112K
  class space    used 4488K, committed 4608K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 8.209 Thread 0x000001da875d23f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001da96a9ee74 relative=0x0000000000000154
Event: 8.209 Thread 0x000001da875d23f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001da96a9ee74 method=java.util.ArrayList.hashCodeRange(II)I @ 53 c2
Event: 8.209 Thread 0x000001da875d23f0 DEOPT PACKING pc=0x000001da96a9ee74 sp=0x0000003ba9cfc6e0
Event: 8.209 Thread 0x000001da875d23f0 DEOPT UNPACKING pc=0x000001da963223a3 sp=0x0000003ba9cfc650 mode 2
Event: 8.209 Thread 0x000001da875d23f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001da96a9ee74 relative=0x0000000000000154
Event: 8.209 Thread 0x000001da875d23f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001da96a9ee74 method=java.util.ArrayList.hashCodeRange(II)I @ 53 c2
Event: 8.209 Thread 0x000001da875d23f0 DEOPT PACKING pc=0x000001da96a9ee74 sp=0x0000003ba9cfc6e0
Event: 8.209 Thread 0x000001da875d23f0 DEOPT UNPACKING pc=0x000001da963223a3 sp=0x0000003ba9cfc650 mode 2
Event: 8.210 Thread 0x000001da875d23f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001da96a9ee74 relative=0x0000000000000154
Event: 8.210 Thread 0x000001da875d23f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001da96a9ee74 method=java.util.ArrayList.hashCodeRange(II)I @ 53 c2
Event: 8.210 Thread 0x000001da875d23f0 DEOPT PACKING pc=0x000001da96a9ee74 sp=0x0000003ba9cfc970
Event: 8.210 Thread 0x000001da875d23f0 DEOPT UNPACKING pc=0x000001da963223a3 sp=0x0000003ba9cfc8e0 mode 2
Event: 8.211 Thread 0x000001da875d23f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001da96a9ee74 relative=0x0000000000000154
Event: 8.211 Thread 0x000001da875d23f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001da96a9ee74 method=java.util.ArrayList.hashCodeRange(II)I @ 53 c2
Event: 8.211 Thread 0x000001da875d23f0 DEOPT PACKING pc=0x000001da96a9ee74 sp=0x0000003ba9cfcd10
Event: 8.211 Thread 0x000001da875d23f0 DEOPT UNPACKING pc=0x000001da963223a3 sp=0x0000003ba9cfcc80 mode 2
Event: 8.353 Thread 0x000001da875d23f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001da96bdb2d8 relative=0x0000000000000238
Event: 8.353 Thread 0x000001da875d23f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001da96bdb2d8 method=org.jetbrains.kotlin.name.Name.hashCode()I @ 16 c2
Event: 8.353 Thread 0x000001da875d23f0 DEOPT PACKING pc=0x000001da96bdb2d8 sp=0x0000003ba9cfd620
Event: 8.353 Thread 0x000001da875d23f0 DEOPT UNPACKING pc=0x000001da963223a3 sp=0x0000003ba9cfd5b8 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.936 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d80378}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623d80378) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.950 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e27dd0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623e27dd0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.954 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e50c18}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623e50c18) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.975 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623f2ffb8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623f2ffb8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.047 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623a51cb8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623a51cb8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.105 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623614f78}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623614f78) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.183 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623315c40}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000623315c40) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.190 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623390410}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000623390410) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.205 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c4c5a0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c4c5a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.123 Thread 0x000001da875d23f0 Implicit null exception at 0x000001da96a46ef2 to 0x000001da96a4754c
Event: 3.374 Thread 0x000001da875d23f0 Implicit null exception at 0x000001da96a4a9f2 to 0x000001da96a4b06c
Event: 3.563 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000621f551d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000621f551d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 5.259 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000624005d50}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x0000000624005d50) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 5.378 Thread 0x000001da875d23f0 Implicit null exception at 0x000001da96b165c1 to 0x000001da96b16768
Event: 5.492 Thread 0x000001da875d23f0 Implicit null exception at 0x000001da96b16050 to 0x000001da96b16188
Event: 5.817 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620d3d840}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000620d3d840) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 5.817 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620d41ec8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000620d41ec8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 5.817 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620d45d38}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000620d45d38) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 8.352 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006078e0ce8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006078e0ce8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 8.425 Thread 0x000001da875d23f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006079e4f00}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006079e4f00) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 6.534 Executing VM operation: HandshakeAllThreads done
Event: 6.641 Executing VM operation: G1CollectForAllocation
Event: 6.648 Executing VM operation: G1CollectForAllocation done
Event: 6.661 Executing VM operation: G1Concurrent
Event: 6.666 Executing VM operation: G1Concurrent done
Event: 6.666 Executing VM operation: G1Concurrent
Event: 6.667 Executing VM operation: G1Concurrent done
Event: 6.930 Executing VM operation: HandshakeAllThreads
Event: 6.974 Executing VM operation: HandshakeAllThreads done
Event: 7.094 Executing VM operation: G1CollectForAllocation
Event: 7.107 Executing VM operation: G1CollectForAllocation done
Event: 7.844 Executing VM operation: ICBufferFull
Event: 7.844 Executing VM operation: ICBufferFull done
Event: 8.081 Executing VM operation: HandshakeAllThreads
Event: 8.081 Executing VM operation: HandshakeAllThreads done
Event: 8.085 Executing VM operation: HandshakeAllThreads
Event: 8.085 Executing VM operation: HandshakeAllThreads done
Event: 8.099 Executing VM operation: HandshakeAllThreads
Event: 8.099 Executing VM operation: HandshakeAllThreads done
Event: 8.555 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 6.357 loading class java/text/StringCharacterIterator done
Event: 6.421 loading class java/util/ArrayList$SubList$1
Event: 6.421 loading class java/util/ArrayList$SubList$1 done
Event: 6.454 Thread 0x000001dafb193b30 Thread added: 0x000001dafb193b30
Event: 6.724 Thread 0x000001dafbc52970 Thread added: 0x000001dafbc52970
Event: 6.757 Thread 0x000001dafbc542c0 Thread added: 0x000001dafbc542c0
Event: 6.759 Thread 0x000001dafbc52e80 Thread added: 0x000001dafbc52e80
Event: 6.763 Thread 0x000001dafbc52460 Thread added: 0x000001dafbc52460
Event: 6.763 Thread 0x000001dafbc547d0 Thread added: 0x000001dafbc547d0
Event: 7.030 Thread 0x000001dafbc547d0 Thread exited: 0x000001dafbc547d0
Event: 7.051 loading class java/util/stream/IntPipeline$1
Event: 7.051 loading class java/util/stream/IntPipeline$1 done
Event: 7.053 loading class java/util/stream/IntPipeline$1$1
Event: 7.053 loading class java/util/stream/IntPipeline$1$1 done
Event: 7.417 Thread 0x000001dafbc52460 Thread exited: 0x000001dafbc52460
Event: 7.418 Thread 0x000001dafbc52e80 Thread exited: 0x000001dafbc52e80
Event: 7.418 Thread 0x000001dafbc542c0 Thread exited: 0x000001dafbc542c0
Event: 7.693 Thread 0x000001dafbc52970 Thread exited: 0x000001dafbc52970
Event: 8.189 Thread 0x000001dafb193b30 Thread exited: 0x000001dafb193b30
Event: 8.209 Thread 0x000001dafbc52e80 Thread added: 0x000001dafbc52e80


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\CategoryAlbumSet\build\20250901_18108630666102982445.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 19:35 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (1315M free)
TotalPageFile size 47840M (AvailPageFile size 202M)
current process WorkingSet (physical memory assigned to process): 201M, peak: 201M
current process commit charge ("private bytes"): 255M, peak: 678M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
