/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FolderPickerUtilTest.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/8/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/8/23       1      create
 ***********************************************************************/
package com.oplus.filemanager.filechoose.utils

import android.content.Intent
import android.net.Uri
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.utils.Log
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import java.io.IOException
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class FolderPickerUtilTest {

    @Before
    fun setup() {
        mockkStatic(FolderPickerUtil::class)
        mockkStatic(Log::class)
        mockkStatic(UriHelper::class)
    }

    @After
    fun teardown() {
        unmockkStatic(FolderPickerUtil::class)
        unmockkStatic(Log::class)
        unmockkStatic(UriHelper::class)
    }

    @Test
    fun `should call grant uri permission to data`() {
        //given
        val intent = Intent()
        val path = ""
        val key = ""
        val uri = Uri.parse("www.aa.bb")
        //when
        FolderPickerUtil.grantUriPermissionToData(intent, path, key)
        //then
        verify { FolderPickerUtil.grantUriPermissionToData(intent, path, key) }

        //when
        every { FolderPickerUtil.grantUriPermissionToData(intent, path, key) } answers { throw IOException() }
        //then
        verify { Log.e(any(), any<String>()) }

        //when
        every { UriHelper.getFileUri(any()) } answers { uri }
        //then
        Assert.assertEquals(intent.data, uri)
    }
}