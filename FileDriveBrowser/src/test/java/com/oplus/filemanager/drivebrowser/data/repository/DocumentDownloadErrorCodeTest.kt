/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : DocumentDownloadErrorCodeTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/2/27 10:51
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/2/27       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.data.repository

import org.junit.Assert
import org.junit.Test

class DocumentDownloadErrorCodeTest {
    @Test
    fun testUnSupportRetryError() {
        val exportErrorCode = 203
        Assert.assertTrue(DocumentDownloadErrorCode.unSupportRetryError(exportErrorCode))

        val noPermissionError = 204
        Assert.assertTrue(DocumentDownloadErrorCode.unSupportRetryError(noPermissionError))

        val unSupportTypeCode = 202
        Assert.assertTrue(DocumentDownloadErrorCode.unSupportRetryError(unSupportTypeCode))
    }
}