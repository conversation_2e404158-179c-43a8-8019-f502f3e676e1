/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IWpsDocThumbnailFactory.kt
 * Description:
 *     Define the factory to create the loader of WPS thumbnail
 *
 * Version: 1.0
 * Date: 2024-08-27
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-08-27   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc.wps

import android.content.Context
import androidx.annotation.Keep
import com.filemanager.thumbnail.doc.IDocThumbnailLoader

@Keep
interface IWpsDocThumbnailFactory {

    /**
     * Create the loader of WPS thumbnail.
     *
     * @param context the context to used in loader
     * @param logger the logger to output log in loader
     */
    fun createWpsThumbnailLoader(
        context: Context,
        logger: IWpsThumbnailLogger
    ): IDocThumbnailLoader
}