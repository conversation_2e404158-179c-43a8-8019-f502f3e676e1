/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchHelper.kt
 ** Description : SearchHelper.kt
 ** Version     : 1.0
 ** Date        : 2024/8/28
 ** Author      : muning
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.exportdmp

import android.app.Activity
import android.content.Context
import android.database.Cursor
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_CONTENT
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_TITLE
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_TITLE_CONTENT
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.oplus.dmp.sdk.GlobalContext
import com.oplus.dmp.sdk.InitConfig
import com.oplus.dmp.sdk.analyzer.IAnalyzer
import com.oplus.dmp.sdk.analyzer.bean.AnalyzedResult
import com.oplus.dmp.sdk.analyzer.local.dict.entity.LocalAnalyzerConfigure
import com.oplus.dmp.sdk.common.log.Logger
import com.oplus.dmp.sdk.index.IndexProxyV2
import com.oplus.dmp.sdk.index.IndexState
import com.oplus.dmp.sdk.querypreprocess.PreProcessType
import com.oplus.dmp.sdk.querypreprocess.ProcessedQuery
import com.oplus.dmp.sdk.querypreprocess.QueryPreProcessorHelper
import com.oplus.dmp.sdk.search.bean.CommonBriefSorter
import com.oplus.dmp.sdk.search.bean.DefaultRecallStrategy
import com.oplus.dmp.sdk.search.bean.FileFolderSorter
import com.oplus.dmp.sdk.search.bean.Filter
import com.oplus.dmp.sdk.search.bean.HitTermNumberFilter
import com.oplus.dmp.sdk.search.bean.RecallStrategy
import com.oplus.dmp.sdk.search.bean.RecallStrategyScoreSorter
import com.oplus.dmp.sdk.search.bean.RecallTypeGroupSorter
import com.oplus.dmp.sdk.search.bean.RecallTypeSorter
import com.oplus.dmp.sdk.search.bean.SearchExpr
import com.oplus.dmp.sdk.search.bean.SearchTerm
import com.oplus.dmp.sdk.search.bean.SortOrder
import com.oplus.dmp.sdk.search.bean.SubstringMatchRecallStrategy
import com.oplus.dmp.sdk.search.bean.SubstringMatchRecallStrategyParam
import com.oplus.dmp.sdk.search.bean.v2.FileHighlighter
import com.oplus.dmp.sdk.search.engine.CustomCursor
import com.oplus.dmp.sdk.search.engine.SearchProxyV2
import com.oplus.dmp.sdk.search.engine.StandardSearchRequest
import com.oplus.dmp.sdk.version.VersionProtocol
import com.oplus.filemanager.exportdmp.SearchManagerUtil.getClientWrapper
import com.oplus.filemanager.exportdmp.SearchManagerUtil.initSyncWrapper
import com.oplus.filemanager.exportdmp.SearchManagerUtil.initWrapper

object SearchHelper {

    const val TAG = "SearchHelper"
    private const val TITLE_FIELD = "searchName"
    private val SUPPORT_VERSIONS = intArrayOf(VersionProtocol.API_VERSION_4)
    const val HIGHLIGHT_BEFORE = 7
    const val HIGHLIGHT_END = 120
    const val HIT_TERM_NUMBER_FILTER = 0.5
    const val PAGE_SIZE_MAX = 200000
    private var isAnalyzerAvailable: Boolean = false
    private var isRemoteServiceAvailable: Boolean = false
    var isServiceAvailable: Boolean = false
    var sIsNormalLightOS: Boolean = FeatureCompat.sIsNormalLightOS
    var sSearchProxyV2: SearchProxyV2? = null
    var sAnalyzer: IAnalyzer? = null

    @VisibleForTesting
    @JvmStatic
    fun init(context: Context?, localAnalyzerConfigure: LocalAnalyzerConfigure?) {
        val config = InitConfig(SUPPORT_VERSIONS, localAnalyzerConfigure)
        initWrapper(context, config)
        Log.d(TAG, "print sdk echo:" + SearchManagerUtil.getInstanceWrapper()?.isServiceAvailable)
    }

    @VisibleForTesting
    @JvmStatic
    fun init(context: Context?) {
        val config = InitConfig(SUPPORT_VERSIONS)
        initWrapper(context, config)
        Log.d(TAG, "print sdk echo:" + SearchManagerUtil.getInstanceWrapper()?.isServiceAvailable)
    }

    @VisibleForTesting
    @JvmStatic
    fun initClient() {
        Log.i(TAG, "initClient start")
        if (sSearchProxyV2 == null || sAnalyzer == null) {
            val client = getClientWrapper("file", "com.oplus.dmp")
            Log.i(TAG, "searchProxy" + client?.searchProxy)
            sSearchProxyV2 = client?.searchProxy as? SearchProxyV2
            sAnalyzer = SearchManagerUtil.getInstanceWrapper()?.analyzer
            Log.i(TAG, "sAnalyzer $sAnalyzer")
        }
    }

    @VisibleForTesting
    @JvmStatic
    fun getCursor(searchKey: String?): Cursor? {
        Log.i(TAG, "getCursor start")
        val request = StandardSearchRequest()
        //第一大步，搜索规则设置
        val rewriteQueries: MutableList<String> = buildRewriteQueries(searchKey)
        //得到分词
        val terms: MutableList<String> = buildAnalyzedTerms(searchKey)
        if (terms.isEmpty()) {
            //获取分词异常时，不执行中子搜索
            return null
        }
        request.setOriginQuery(searchKey)
        //召回策略处理
        val recallStrategyList = mutableListOf<RecallStrategy>()
        Log.i(TAG, "set recallStrategyList")
        //精准匹配 标题+内容
        val titleContentRecallStrategy =
            buildMatchRecallStrategy(searchKey, RECALL_TYPE_TITLE_CONTENT)
        //精准匹配 标题
        val titleRecallStrategy = buildMatchRecallStrategy(searchKey, RECALL_TYPE_TITLE)
        //精准匹配 内容
        val contentRecallStrategy =
            buildMatchRecallStrategy(searchKey, RECALL_TYPE_CONTENT)
        recallStrategyList.add(titleContentRecallStrategy) //0文件名+内容
        recallStrategyList.add(titleRecallStrategy)  //1文件名
        recallStrategyList.add(contentRecallStrategy)  //2内容
        //第二大步,排序规则设置和设置召回策略
        val builder = handleBuilder(terms, rewriteQueries, searchKey)
        val expr = builder.build()
        val defaultRecallStrategy = DefaultRecallStrategy(expr)
        Log.i(TAG, "defaultRecallStrategy")
        //模糊搜索文件名的策略
        recallStrategyList.add(defaultRecallStrategy)   //3模糊搜索文件名
        handleRequestSorter(
            titleRecallStrategy,
            contentRecallStrategy,
            defaultRecallStrategy,
            recallStrategyList,
            request
        )
        handleRequestParam(request, recallStrategyList)
        val cursor = sSearchProxyV2?.search(request)
        Log.i(TAG, "search cursor $cursor")
        cursor?.let { cursor ->
            Log.i(TAG, "cursor_count:" + cursor.count)
        }
        return cursor
    }

    @VisibleForTesting
    @JvmStatic
    fun handleRequestSorter(
        titleRecallStrategy: RecallStrategy,
        contentRecallStrategy: RecallStrategy,
        defaultRecallStrategy: DefaultRecallStrategy,
        recallStrategyList: MutableList<RecallStrategy>,
        request: StandardSearchRequest
    ) {
        // 同时命中标题与内容，排第一
        val stage1 = listOf(titleRecallStrategy, contentRecallStrategy)
        //只命中标题排第二
        val stage2 = listOf(titleRecallStrategy)
        //只命中内容排第三
        val stage3 = listOf(contentRecallStrategy)
        //倒排命中标题排第四
        val stage4 = listOf<RecallStrategy>(defaultRecallStrategy)
        //按照召回策略排序
        val recallTypeSorter = RecallTypeSorter(
            recallStrategyList,
            listOf(stage1, stage2, stage3, stage4)
        )
        //按照时间排序
        val timeSorter = CommonBriefSorter("briefTime", SortOrder.DESC)
        Log.i(TAG, "timeSorter")
        //由于各个命中的策略要求的组内的排序策略不同，因此使用GroupSorter,stage3为倒排召回策略，在该策略中，使用得分进行排序
        val stageSorterMap =
            mapOf(Pair(stage4, RecallStrategyScoreSorter(defaultRecallStrategy, recallStrategyList)))
        val recallTypeGroupSorter = RecallTypeGroupSorter(
            recallTypeSorter,
            recallStrategyList,
            stageSorterMap
        )
        //排序规则合集  标签第一  文件夹第二  文件第三  外加修改时间倒序
        request.sorters = listOf(
            FileFolderSorter(SortOrder.DESC),
            recallTypeGroupSorter,
            timeSorter
        )
        Log.i(TAG, "request.sorters")
    }

    @VisibleForTesting
    @JvmStatic
    fun handleRequestParam(
        request: StandardSearchRequest,
        recallStrategyList: MutableList<RecallStrategy>
    ) {
        //设置request参数
        request.recallStrategies = recallStrategyList
        request.columnNames =
            listOf("absolutePath", "filename", "briefTime", "type", "size", "lastModified")
        request.pageNum = 1
        request.pageSize = PAGE_SIZE_MAX
        //中子类型分类  image 1;audio 2; video 3;doc 4;apk 5;archive 6; folder 7
        request.filters = getHiFilter()
        //过滤命中term的数量
        Log.i(TAG, "request.filters")
        //设置高亮
        request.highlighters = listOf(
            FileHighlighter(FileHighlighter.CONTENT_FIELD, HIGHLIGHT_BEFORE, HIGHLIGHT_END),
            FileHighlighter(FileHighlighter.TITLE_FIELD, HIGHLIGHT_BEFORE, HIGHLIGHT_END)
        )
        Log.i(TAG, "set highlighters")
    }

    @VisibleForTesting
    @JvmStatic
    fun getHiFilter(): List<Filter> {
        return listOf(HitTermNumberFilter(HIT_TERM_NUMBER_FILTER))
    }

    @VisibleForTesting
    @JvmStatic
    fun handleBuilder(
        terms: MutableList<String>,
        rewriteQueries: MutableList<String>,
        searchKey: String?
    ): SearchExpr.Builder {
        val builder = SearchExpr.Builder()
        // 标题的倒排检索
        for (term in terms) {
            builder.orHave(SearchTerm(term, TITLE_FIELD))
        }
        // 标题倒排检索命中了转写query，不应该过滤
        for (rewriteQuery in rewriteQueries) {
            Log.d(TAG, "rewriteQuery :$rewriteQuery")
            if (rewriteQuery != searchKey) {
                val searchTerm = SearchTerm(rewriteQuery, TITLE_FIELD)
                searchTerm.isSynonym = true
                builder.orHave(searchTerm)
            }
        }
        return builder
    }

    @JvmStatic
    fun buildMatchRecallStrategy(searchKey: String?, recallType: Int): RecallStrategy {
        return when (recallType) {
            RECALL_TYPE_TITLE_CONTENT -> {
                val paramTitleContent = SubstringMatchRecallStrategyParam(
                    searchKey, SearchTerm.MatchPattern.SUBSTRING,
                    SubstringMatchRecallStrategyParam.FieldRelation.AND,
                    true, "title"
                )
                SubstringMatchRecallStrategy(paramTitleContent)
            }

            RECALL_TYPE_TITLE -> {
                val paramTitle = SubstringMatchRecallStrategyParam(
                    searchKey, SearchTerm.MatchPattern.SUBSTRING,
                    SubstringMatchRecallStrategyParam.FieldRelation.AND,
                    false, "title"
                )
                SubstringMatchRecallStrategy(paramTitle)
            }

            else -> {
                val paramContent =
                    SubstringMatchRecallStrategyParam(
                        searchKey, SearchTerm.MatchPattern.SUBSTRING,
                        SubstringMatchRecallStrategyParam.FieldRelation.AND,
                        true
                    )
                SubstringMatchRecallStrategy(paramContent)
            }
        }
    }

    @JvmStatic
    fun buildAnalyzedTerms(searchKey: String?): MutableList<String> {
        Log.i(TAG, "get terms")
        val terms = mutableListOf<String>()
        var analyzedResult: AnalyzedResult? = null
        kotlin.runCatching {
            analyzedResult = sAnalyzer?.analyze(searchKey)
        }.onFailure {
            Log.d(TAG, "buildAnalyzedTerms :${it.message}")
            return terms
        }
        val analyzedTerms = analyzedResult?.originalQueryAnalyzeResult?.analyzedTerms
        analyzedTerms?.let {
            for (analyzedTerm in analyzedTerms) {
                terms.add(analyzedTerm.word)
                Log.d(TAG, "term_word :${analyzedTerm.word}")
            }
        }
        return terms
    }

    @JvmStatic
    fun buildRewriteQueries(searchKey: String?): MutableList<String> {
        //临时处理屏蔽掉%号的转写功能
        searchKey?.let {
            if (it.contains("%") || it.contains("％")) {
                return mutableListOf()
            }
        }
        val queryPreProcessorHelper =
            QueryPreProcessorHelper(PreProcessType.NORMALIZE, PreProcessType.ERROR_CORRECT, PreProcessType.SYNONYM)
        //预处理
        val queryPreProcessContext = queryPreProcessorHelper.doPreProcess(searchKey)
        //得到转写词
        val rewriteQueries = mutableListOf<String>()
        for (processedQuery in queryPreProcessContext.results) {
            if (processedQuery.type == ProcessedQuery.SYNONYM) {
                Log.d(TAG, "add rewrittenQuery:${processedQuery.rewrittenQuery}")
                if (TextUtils.isEmpty(processedQuery.rewrittenQuery).not()) {
                    rewriteQueries.add(processedQuery.rewrittenQuery)
                }
            }
        }
        return rewriteQueries
    }

    /**
     * 检查dmp有没有所有文件权限
     */
    @JvmStatic
    fun checkState(activity: Activity) {
        Log.d(TAG, "dmpsearch checkState")
        val proxyV2 = IndexProxyV2("file") // 当前支持 file & gallery
        val indexState = proxyV2.state
        Log.d(TAG, "indexState: $indexState")
        if (indexState == IndexState.INDEX_STATE_NO_PERMISSION) {
            Log.d(TAG, "indexState: INDEX_STATE_NO_PERMISSION")
            // 拉起权限授权界面
            proxyV2.requestPermission(activity)
        }
    }

    @JvmStatic
    fun isDMPCursor(cursor: Cursor): Boolean {
        return cursor is CustomCursor
    }

    @JvmStatic
    fun initDMPSearch() {
        Log.i(TAG, "initDMPSearch")
        Logger.init(true)
        GlobalContext.setContext(appContext)
        val localAnalyzerConfigure = LocalAnalyzerConfigure.Builder()
            .setFilterStopWord(false)
            .setCutAll(false)
            .setAsTermDictUseNerDict(true)
            .build()
        val config = InitConfig(SUPPORT_VERSIONS, localAnalyzerConfigure)
        initWrapper(appContext, config)
        getStatusAfterInitDmp()
    }

    /**
     * sdk初始化，包含api版本号设置以及分词词典相关设置。
     */
    fun init(context: Context): Boolean {
        val supportVersions = intArrayOf(VersionProtocol.API_VERSION_2, VersionProtocol.API_VERSION_3)
        /*
        val synonymDictConfig = DictConfig.Builder()
            .setDictName("synonym")
            .setDictFileFolderPath("dict/")
            .setDictFileName("synonym.txt")
            .setDictType(DictConfig.DictType.PLAINTEXT_KV)
            .setSplitRegex(";")
            .setElemType(DictConfig.ElemType.STRING)
            .setCache(true)
            .setMd5(null)
            .setFileLocationType(DictConfig.FileLocationType.ASSETS).build()
         */
        val localAnalyzerConfigure = LocalAnalyzerConfigure.Builder()
//            .setSynonymDictConfig(synonymDictConfig)
            .setCutAll(false)
            .setAsTermDictUseNerDict(true)
            .build()
        val initConfig = InitConfig(supportVersions, localAnalyzerConfigure)
        val initResult = initSyncWrapper(context, initConfig)
        Log.i(TAG, "init initConfig $initConfig initResult $initResult")
        //这里调用本地搜索的的初始化内部变量的逻辑
        getStatusAfterInitDmp()
        return initResult
    }

    private fun getStatusAfterInitDmp() {
        Log.d(TAG, "isServiceAvailable start")
        isServiceAvailable = SearchManagerUtil.getInstanceWrapper()?.isServiceAvailable ?: false
        Log.d(TAG, "isRemoteServiceAvailable start")
        isRemoteServiceAvailable = SearchManagerUtil.getInstanceWrapper()?.isRemoteServiceAvailable
            ?: false
        Log.d(TAG, "isAnalyzerAvailable start")
        isAnalyzerAvailable = SearchManagerUtil.getInstanceWrapper()?.isAnalyzerAvailable ?: false
        Log.d(
            TAG, "initDMPSearch ServiceAva $isServiceAvailable, " +
                    "remoteServiceAva $isRemoteServiceAvailable, analyzerAva $isAnalyzerAvailable"
        )
    }

    /**
     * 是否支持中子搜索
     */
    @JvmStatic
    fun isShouldLoadDMP(): Boolean {
        //轻量（非新轻量） 中子搜索
        if (sIsNormalLightOS || !SdkUtils.isAtLeastOS15()) {
            return false
        }
        val shouldLoadDMP =
            isAnalyzerAvailable && isServiceAvailable && isRemoteServiceAvailable
        Log.d(TAG, "shouldLoadDMP : $shouldLoadDMP")
        return shouldLoadDMP
    }
}