/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentFilesCardWidgetProvider
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/6
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/6      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.provider

import android.content.Context
import android.content.res.Configuration
import androidx.annotation.VisibleForTesting
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.noMoreAction
import com.oplus.cardwidget.domain.action.CardWidgetAction
import com.oplus.cardwidget.serviceLayer.AppCardWidgetProvider
import com.oplus.cardwidget.util.getCardType
import com.oplus.filemanager.cardwidget.data.RecentFileCardData
import com.oplus.filemanager.cardwidget.ext.mapperToRecentFileCardItemData
import com.oplus.filemanager.cardwidget.packer.RecentFileCardItemDataPacker
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_2_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_2_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_4_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_4_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_4_TO_4_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_4_TO_4_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CardWidgetThumbnailGenerator
import com.oplus.filemanager.cardwidget.util.LAYOUT_2_TO_2_MAX_ITEM_COUNT
import com.oplus.filemanager.cardwidget.util.LAYOUT_2_TO_4_MAX_ITEM_COUNT
import com.oplus.filemanager.cardwidget.util.LAYOUT_4_TO_4_MAX_ITEM_COUNT
import com.oplus.filemanager.recent.entity.recent.ExpandGroupItemEntity
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import com.oplus.filemanager.recent.task.RecentLoadCallback
import com.oplus.filemanager.recent.utils.RecentDataHelper
import com.oplus.filemanager.recent.utils.RecentFileObserver
import java.io.File
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.abs

@Suppress("TooGenericExceptionCaught")
class RecentFilesCardWidgetProvider : AppCardWidgetProvider(),
    RecentLoadCallback {

    var mLastRefreshTime = 0L
        get() = field.noMoreAction()

    companion object {
        const val TAG = "RecentFilesCardWidgetProvider"
        const val MIN_REFRESH_TIME_INTERVAL = 1000L * 5

        var widgetCodeList = CopyOnWriteArrayList<String>(mutableListOf())
        var mRecentFiles: MutableList<RecentFileEntity> = mutableListOf()
        var mOpenFile: RecentFileEntity? = null
        val mCardWidgetLayoutMaps = mutableMapOf<String, String>()

        val instance: RecentFilesCardWidgetProvider by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            RecentFilesCardWidgetProvider()
        }

        @VisibleForTesting
        const val LAYOUT_NAME_2_TO_2 = "recent_files_card_widget_layout_2_to_2.json"

        @VisibleForTesting
        const val LAYOUT_NAME_2_TO_4 = "recent_files_card_widget_layout_2_to_4.json"

        @VisibleForTesting
        const val LAYOUT_NAME_4_TO_4 = "recent_files_card_widget_layout_4_to_4.json"
        const val NO_PERMISSION_LAYOUT = "no_permission_layout.json"

        @VisibleForTesting
        const val EMPTY_2_TO_2 = "empty_data_2_to_2.json"
        @VisibleForTesting
        const val EMPTY_2_TO_4 = "empty_data_2_to_4.json"
        @VisibleForTesting
        const val EMPTY_4_TO_4 = "empty_data_4_to_4.json"
    }
    private var mRecentFileObserver = RecentFileObserver()

    override fun getCardLayoutName(widgetCode: String): String {
        return getCardLayoutNameByCardType(widgetCode.getCardType())
    }

    @VisibleForTesting
    fun getCardLayoutNameByCardType(cardType: Int): String {
        return (when (cardType) {
            CARD_TYPE_2_TO_2_COLOROS, CARD_TYPE_2_TO_2_ONEPLUS -> LAYOUT_NAME_2_TO_2
            CARD_TYPE_2_TO_4_COLOROS, CARD_TYPE_2_TO_4_ONEPLUS -> LAYOUT_NAME_2_TO_4
            CARD_TYPE_4_TO_4_COLOROS, CARD_TYPE_4_TO_4_ONEPLUS -> LAYOUT_NAME_4_TO_4
            else -> LAYOUT_NAME_2_TO_2
        })
    }

    override fun subscribed(context: Context, widgetCode: String) {
        super.subscribed(context, widgetCode)
        Log.v(TAG, "subscribed widgetCode=$widgetCode")
        widgetCodeList.add(widgetCode)
    }

    override fun unSubscribed(context: Context, widgetCode: String) {
        super.unSubscribed(context, widgetCode)
        Log.v(TAG, "unSubscribed widgetCode=$widgetCode")
        if (widgetCodeList.iterator().hasNext()) {
            widgetCodeList.remove(widgetCode)
        }
        mCardWidgetLayoutMaps.remove(widgetCode)
    }

    override fun onResume(context: Context, widgetCode: String) {
        Log.d(TAG, "onResume widgetCode=$widgetCode")
        refreshIfNeed()
    }

    override fun onDestroy(context: Context, widgetCode: String) {
        super.onDestroy(context, widgetCode)
        mRecentFileObserver.destroy()
    }

    @VisibleForTesting
    fun refreshIfNeed() {
        if (abs(System.currentTimeMillis() - mLastRefreshTime) < MIN_REFRESH_TIME_INTERVAL) {
            Log.d(TAG, "refreshIfNeed refresh too fast, ignore this refresh request")
            return
        }
        Log.d(TAG, "refreshIfNeed do refresh")
        initWidgetCode()
        val isPrivacyGranted = PrivacyPolicyController.hasAgreePrivacy()
        val isPermissionGranted = PermissionUtils.hasStoragePermission()
        refreshData(isPrivacyGranted, isPermissionGranted)
    }

    @VisibleForTesting
    fun initWidgetCode() {
        Log.i(TAG, "initWidgetCode")
        val showSize = getShowedCardList().size
        Log.i(TAG, "showSize = $showSize")
        if (widgetCodeList.size == 0 && showSize != 0) {
            Log.i(TAG, "clear data")
            widgetCodeList = CopyOnWriteArrayList(getShowedCardList().toTypedArray())
        }
        if (widgetCodeList.size == 0) {
            Log.i(TAG, "no card")
            return
        }
        for (widgetCode in widgetCodeList) {
            Log.i(TAG, "widgetCode: $widgetCode \t")
        }
    }

    @VisibleForTesting
    fun refreshData(isPrivacyGranted: Boolean, isStoragePermissionGranted: Boolean) {
        val cardData = createData()
        if (isPrivacyGranted && isStoragePermissionGranted) {
            refreshDataNormally(cardData)
        } else if (!isPrivacyGranted) {
            refreshDataPrivacyNotGranted(cardData)
        } else if (!isStoragePermissionGranted) {
            refreshDataStoragePermissionNotGranted(cardData)
        }
    }

    @VisibleForTesting
    fun refreshDataStoragePermissionNotGranted(cardData: RecentFileCardData) {
        cardData.uiState = RecentFileCardData.UI_STATE_NO_STORAGE_PERMISSION
        for (widgetCode in widgetCodeList) {
            if (mCardWidgetLayoutMaps[widgetCode].equals(NO_PERMISSION_LAYOUT).not()) {
                CardWidgetAction.switchLayoutCommand(widgetCode, NO_PERMISSION_LAYOUT)
                mCardWidgetLayoutMaps[widgetCode] = NO_PERMISSION_LAYOUT
            }
            val context = this.context ?: return
            CardWidgetAction.postUpdateCommand(context,
                RecentFileCardItemDataPacker(cardData, widgetCode, context),
                widgetCode
            )
        }
    }

    @VisibleForTesting
    fun refreshDataPrivacyNotGranted(cardData: RecentFileCardData) {
        cardData.uiState = RecentFileCardData.UI_STATE_NO_PRIVACY
        for (widgetCode in widgetCodeList) {
            if (mCardWidgetLayoutMaps[widgetCode].equals(NO_PERMISSION_LAYOUT).not()) {
                CardWidgetAction.switchLayoutCommand(widgetCode, NO_PERMISSION_LAYOUT)
                mCardWidgetLayoutMaps[widgetCode] = NO_PERMISSION_LAYOUT
            }
            val context = this.context ?: return
            CardWidgetAction.postUpdateCommand(context,
                RecentFileCardItemDataPacker(cardData, widgetCode, context),
                widgetCode
            )
        }
    }

    @VisibleForTesting
    fun getEmptyLayout(cardType: Int): String {
        return when (cardType) {
            CARD_TYPE_2_TO_2_COLOROS, CARD_TYPE_2_TO_2_ONEPLUS -> EMPTY_2_TO_2
            CARD_TYPE_2_TO_4_COLOROS, CARD_TYPE_2_TO_4_ONEPLUS -> EMPTY_2_TO_4
            CARD_TYPE_4_TO_4_COLOROS, CARD_TYPE_4_TO_4_ONEPLUS -> EMPTY_4_TO_4
            else -> EMPTY_2_TO_2
        }
    }

    @VisibleForTesting
    fun refreshDataNormally(cardData: RecentFileCardData) {
        Log.d(TAG, "refreshDataNormally")
        runOnCardThread {
            kotlin.runCatching {
                // real load data work
                loadRecentFiles()
            }.onSuccess {
                //refreshDataNormallySuccess(cardData)
            }.onFailure {
                refreshDataNormallyFail(cardData)
            }
        }
    }

    private fun refreshDataNormallyFail(cardData: RecentFileCardData) {
        cardData.uiState = RecentFileCardData.UI_STATE_LOADING_FAIL
        Log.i(TAG, "refreshDataNormallyFail postUpdateCommand onFailure")
        for (widgetCode in widgetCodeList) {
            val layout = getCardLayoutName(widgetCode)
            if (mCardWidgetLayoutMaps[widgetCode].equals(layout).not()) {
                CardWidgetAction.switchLayoutCommand(widgetCode, layout)
                mCardWidgetLayoutMaps[widgetCode] = layout
            }
            val context = this.context ?: return
            CardWidgetAction.postUpdateCommand(context,
                RecentFileCardItemDataPacker(cardData, widgetCode, context),
                widgetCode
            )
        }
    }

    private fun refreshDataNormallySuccess(cardData: RecentFileCardData) {
        for (widgetCode in widgetCodeList) {
            if (cardData.recentFiles.isEmpty()) {
                cardData.uiState = RecentFileCardData.UI_STATE_LOADING_EMPTY
                val layout = getEmptyLayout(widgetCode.getCardType())
                if (mCardWidgetLayoutMaps[widgetCode].equals(layout).not()) {
                    CardWidgetAction.switchLayoutCommand(widgetCode, layout)
                    mCardWidgetLayoutMaps[widgetCode] = layout
                }
            } else {
                cardData.uiState = RecentFileCardData.UI_STATE_LOADING_SUCCESS
                val layout = getCardLayoutName(widgetCode)
                if (mCardWidgetLayoutMaps[widgetCode].equals(layout).not()) {
                    CardWidgetAction.switchLayoutCommand(widgetCode, layout)
                    mCardWidgetLayoutMaps[widgetCode] = layout
                }
            }
            val context = this.context ?: return
            CardWidgetAction.postUpdateCommand(context,
                RecentFileCardItemDataPacker(cardData, widgetCode, context),
                widgetCode
            )
        }
    }

    @VisibleForTesting
    fun getMaxCountOfCardItems(): Int {
        val maxCount: Int
        var has22 = false
        var has24 = false
        var has44 = false
        for (widgetCode in widgetCodeList) {
            when (widgetCode.getCardType()) {
                CARD_TYPE_2_TO_2_COLOROS, CARD_TYPE_2_TO_2_ONEPLUS -> has22 = true
                CARD_TYPE_2_TO_4_COLOROS, CARD_TYPE_2_TO_4_ONEPLUS -> has24 = true
                CARD_TYPE_4_TO_4_COLOROS, CARD_TYPE_4_TO_4_ONEPLUS -> has44 = true
            }
        }
        maxCount = when {
            has44 -> {
                LAYOUT_4_TO_4_MAX_ITEM_COUNT
            }
            has24 -> {
                LAYOUT_2_TO_4_MAX_ITEM_COUNT
            }
            has22 -> {
                LAYOUT_2_TO_2_MAX_ITEM_COUNT
            }
            else -> {
                LAYOUT_2_TO_2_MAX_ITEM_COUNT
            }
        }
        return maxCount
    }

    private fun loadRecentFiles() {
        Log.v(TAG, "loadRecentFiles start")
        mRecentFileObserver.loadRecentData(RecentDataHelper.TYPE_MEDIA, null, this)
    }

    override fun loadSucc(type: Int, data: MutableList<ExpandGroupItemEntity>?) {
        Log.v(TAG, "RecentLoadCallback --> loadSucc")
        mLastRefreshTime = System.currentTimeMillis()
        mRecentFiles = getRecentFilesFromSourceData(data)
        Log.v(TAG, "RecentLoadCallback --> loadSucc mRecentFiles.size = ${mRecentFiles.size}")
        val maxCount: Int = getMaxCountOfCardItems()
        val cardData = RecentFileCardData()
        if (mRecentFiles.isNotEmpty()) {
            mRecentFiles = mRecentFiles.subList(
                0,
                if (mRecentFiles.size > maxCount) maxCount else mRecentFiles.size
            )
            mRecentFiles.forEach { recentFileEntity ->
                cardData.recentFiles.add(recentFileEntity.mapperToRecentFileCardItemData())
                CardWidgetThumbnailGenerator.generate(
                    recentFileEntity.mLocalType,
                    recentFileEntity.mAbsolutePath,
                    recentFileEntity.mDateModified,
                    recentFileEntity.mSize
                )
            }
        }
        refreshDataNormallySuccess(cardData)
    }

    @VisibleForTesting
    fun getRecentFilesFromSourceData(sourceList: MutableList<ExpandGroupItemEntity>?): MutableList<RecentFileEntity> {
        val result: MutableList<RecentFileEntity> = mutableListOf()
        if (sourceList.isNullOrEmpty()) {
            return result
        } else {
            sourceList.apply {
                for (groupItemEntity in this) {
                    for (fileEntity in groupItemEntity.getChildList()) {
                        if (fileEntity == null) {
                            continue
                        }
                        if (fileEntity.mSize == 0L) {
                            try {
                                //mSize will return 0 sometimes when query from MediaStore, so need check from Java_File_Interface again
                                val file = File(fileEntity.mAbsolutePath)
                                fileEntity.mSize = file.length()
                                fileEntity.mLastModified =
                                    file.lastModified() / KtConstants.SECONDS_TO_MILLISECONDS
                            } catch (e: Exception) {
                                Log.e(TAG, e.message)
                            }
                        }
                        result.add(fileEntity)
                    }
                }
            }
        }
        return result
    }

    override fun loadFail(type: Int, msgObj: Any?) {
        refreshDataNormallyFail(RecentFileCardData())
    }

    @VisibleForTesting
    fun createData() = RecentFileCardData()

    override fun loadInvalid() {
        // do nothing
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mLastRefreshTime = 0L
        mCardWidgetLayoutMaps.clear()
    }
}