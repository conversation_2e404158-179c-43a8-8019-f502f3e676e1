package com.oplus.filemanager.remotedevice

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.os.Looper
import com.filemanager.common.bean.remotedevice.Constants
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.bean.remotedevice.ThumbnailInfo
import com.filemanager.common.constants.Constants.REMOTE_RECENT_DIR_PATH
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.remotedevice.*
import com.oplus.remotecontrol.remotecontrolsdk.RemoteControlServiceManager
import com.oplus.remotecontrol.remotecontrolsdk.bean.ThumbnailResult
import com.oplus.remotecontrol.remotecontrolsdk.callback.IRcDeviceListCallback
import com.oplus.remotecontrol.remotecontrolsdk.callback.IRcDeviceListChangeListener
import com.oplus.remotecontrol.remotecontrolsdk.callback.IRcGetThumbnailCallback
import com.oplus.remotecontrol.remotecontrolsdk.callback.IRcQueryFileListCallback
import com.oplus.remotecontrol.remotecontrolsdk.callback.IRcResultCallback
import com.oplus.remotecontrol.remotecontrolsdk.constants.DeviceState
import com.oplus.remotecontrol.remotecontrolsdk.constants.ResultConstant
import com.oplus.remotecontrol.remotecontrolsdk.db.DeviceInfo
import com.oplus.remotecontrol.remotecontrolsdk.db.Square
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * RemoteDeviceManager的单元测试类
 * 用于测试RemoteDeviceManager的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RemoteDeviceManagerTest {

    // 模拟对象声明
    private lateinit var mockContext: Context
    private lateinit var mockActivity: Activity
    private lateinit var mockRemoteControlServiceManager: RemoteControlServiceManager
    private lateinit var mockDeviceInfo: DeviceInfo
    private lateinit var mockRemoteDeviceInfo: RemoteDeviceInfo
    private lateinit var mockRemoteFileData: RemoteFileData
    private lateinit var mockThumbnailResult: ThumbnailResult

    /**
     * 测试前的初始化方法
     * 1. 模拟Log类
     * 2. 初始化各种mock对象
     * 3. 设置RemoteDeviceManager的mock行为
     */
    @Before
    fun setUp() {
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } just Runs
        every { Log.e(any(), any<String>()) } just Runs
        every { Log.e(any(), any<String>(), any<Throwable>()) } just Runs

        // 初始化mock对象
        mockContext = mockk(relaxed = true)
        mockActivity = mockk()
        mockRemoteControlServiceManager = mockk(relaxed = true)
        mockDeviceInfo = mockk()
        mockRemoteDeviceInfo = mockk()
        mockRemoteFileData = mockk()
        mockThumbnailResult = mockk()

        // 模拟RemoteDeviceManager的单例对象
        mockkObject(RemoteDeviceManager)
        every { RemoteDeviceManager.getCurrentLinkeInfo() } returns mockRemoteDeviceInfo
        every { RemoteDeviceManager.getDeviceList(any()) } answers {
            firstArg<(List<RemoteDeviceInfo>) -> Unit>().invoke(listOf(mockRemoteDeviceInfo))
        }
        every { mockContext.applicationContext } returns mockContext
    }

    /**
     * 测试后的清理方法
     * 1. 销毁RemoteDeviceManager
     * 2. 解除所有mock
     */
    @After
    fun tearDown() {
        RemoteDeviceManager.destroy()
        unmockkAll()
    }

    /**
     * 测试初始化方法
     * 验证RemoteDeviceManager.init()方法是否被正确调用
     */
    @Test
    fun testInit() {
        RemoteDeviceManager.init(mockContext)
        verify { RemoteDeviceManager.init(mockContext) }
    }

    /**
     * 测试是否有其他设备正在连接的方法
     * 1. 测试当有设备连接时返回true
     * 2. 测试当没有设备连接时返回false
     */
    @Test
    fun testHasOtherConnectingDevice() {
        // 测试有设备连接的情况
        RemoteDeviceManager.connectingStatus["device1"] = true
        assertTrue(RemoteDeviceManager.hasOtherConnectingDevice())
        
        // 测试没有设备连接的情况
        RemoteDeviceManager.connectingStatus.clear()
        assertFalse(RemoteDeviceManager.hasOtherConnectingDevice())
    }

    /**
     * 测试获取设备列表的方法
     * 验证设备列表回调是否被正确调用
     */
    @Test
    fun testGetDeviceList() {
        val mockCallback: (List<RemoteDeviceInfo>) -> Unit = mockk()
        every { mockCallback.invoke(any()) } just Runs

        // 设置mock行为
        every { mockRemoteControlServiceManager.getSupportFileSearch() } returns true
        every { mockRemoteControlServiceManager.getAppSettingsState() } returns true
        every { mockRemoteControlServiceManager.queryDeviceList(any()) } answers {
            firstArg<IRcDeviceListCallback>().onResult(
                ResultConstant.RESULT_CODE_SUCCESS,
                listOf(mockDeviceInfo)
            )
        }

        RemoteDeviceManager.getDeviceList(mockCallback)
        verify { mockCallback.invoke(any()) }
    }

    /**
     * 测试添加和移除设备状态监听器的方法
     * 验证监听器的添加和移除操作是否被正确执行
     */
    @Test
    fun testAddRemoveDeviceStatusListener() {
        val mockListener: IRemoteDeviceStatusListener = mockk()
        // 测试添加监听器
        RemoteDeviceManager.addDeviceStatusListener(mockListener)
        verify { RemoteDeviceManager.addDeviceStatusListener(mockListener) }
        
        // 测试移除监听器
        RemoteDeviceManager.removeDeviceStatusListener(mockListener)
        verify { RemoteDeviceManager.removeDeviceStatusListener(mockListener) }
    }

    /**
     * 测试获取当前连接设备信息的方法
     * 验证返回的设备信息是否正确
     */
    @Test
    fun testGetCurrentLinkeInfo() {
        every { RemoteDeviceManager.getCurrentLinkeInfo() } returns mockRemoteDeviceInfo
        assertEquals(mockRemoteDeviceInfo, RemoteDeviceManager.getCurrentLinkeInfo())
    }

    /**
     * 测试获取用户路径的方法
     * 验证路径获取功能是否正常工作
     */
    @Test
    fun testGetUserPath() {
        // 设置mock行为
        every { mockRemoteDeviceInfo.deviceId } returns "device1"
        every { mockRemoteControlServiceManager.queryUserPath(any(), any()) } answers {
            secondArg<IRcResultCallback>().onResult(
                ResultConstant.RESULT_CODE_SUCCESS,
                "/user/path"
            )
        }

        val result = RemoteDeviceManager.getUserPath(onlyGetCache = false)
        assertEquals(11, result.errorCode)  // 验证返回的错误码是否正确
    }
}