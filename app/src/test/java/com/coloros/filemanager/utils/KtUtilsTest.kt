package com.coloros.filemanager.utils

import android.content.Context
import android.os.LocaleList
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants.*
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.KtUtils.STORAGE_EXTERNAL
import com.filemanager.common.utils.KtUtils.STORAGE_INTERNAL
import com.filemanager.common.utils.KtUtils.STORAGE_INTERNAL_MULTI_APP
import com.filemanager.common.utils.KtUtils.STORAGE_OTG
import com.filemanager.common.utils.Utils
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File
import java.util.*

class KtUtilsTest {
    @MockK
    lateinit var mFile: BaseFileBean

    @MockK
    lateinit var mContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mFile = mockk(relaxed = true)
        mContext = mockk(relaxed = true)
    }

    @Test
    fun `should verify when format size without`() {
        Assert.assertEquals("", KtUtils.formatSize(null))

        mockkObject(KtUtils)
        every { mFile.mIsDirectory }.returns(true)
        Assert.assertEquals("", KtUtils.formatSize(mFile))

        mockkStatic(Utils::class)
        every { mFile.mIsDirectory }.returns(false)
        every { mFile.mSize }.returns(100)
        every { Utils.byteCountToDisplaySize(any()) }.returns("test")
        Assert.assertEquals("test", KtUtils.formatSize(mFile))
    }

    @Test
    fun `should verify when get application detail format without`() {
        mockkObject(KtUtils)
        Assert.assertEquals("", KtUtils.getApplicationDetailFormat(null, "", "", mFile))

        mockkStatic(TextUtils::class)

        every { TextUtils.isEmpty("null") }.returns(true)
        Assert.assertEquals("", KtUtils.getApplicationDetailFormat(mContext, "", "null", mFile))

        every { KtUtils.formatSize(any()) }.returns("null")
        Assert.assertEquals("", KtUtils.getApplicationDetailFormat(mContext, "test", "this is a test", mFile))


        every { KtUtils.formatSize(any()) }.returns("apkDetail test")
        mockkStatic(Utils::class)
        every { Utils.isRtl() }.returns(true)
        Assert.assertTrue(KtUtils.getApplicationDetailFormat(mContext, "test", "this is a test", mFile).contains("\u202C"))
    }

    @Test
    fun `should verify when need filter by position without`() {
        Assert.assertFalse(KtUtils.needFilterByPosition(TAB_ALL, -1))
        Assert.assertTrue(KtUtils.needFilterByPosition(TAB_IMAGE, -1))
        Assert.assertTrue(KtUtils.needFilterByPosition(TAB_VIDEO, -1))
        Assert.assertTrue(KtUtils.needFilterByPosition(TAB_AUDIO, -1))
        Assert.assertTrue(KtUtils.needFilterByPosition(TAB_DOCUMENT, -1))
        Assert.assertTrue(KtUtils.needFilterByPosition(TAB_OTHER, MimeTypeHelper.TXT_TYPE))
        Assert.assertFalse(KtUtils.needFilterByPosition(-1, -1))
    }

    @Test
    fun `should verify when get parent file path without`() {
        Assert.assertEquals("", KtUtils.getParentFilePath("test"))
        Assert.assertEquals("D:" + File.separator + "hm", KtUtils.getParentFilePath("D:" + File.separator + "hm" + File.separator + "test"))
    }

    @Test
    fun `should verify when get file name with out extension without`() {
        Assert.assertEquals("", KtUtils.getFileNameWithOutExtension("test"))
        Assert.assertEquals("hm.zj", KtUtils.getFileNameWithOutExtension("hm.zj.test"))
    }

    @Test
    fun `should verify when format video time without`() {
        Assert.assertEquals("30:00", KtUtils.formatVideoTime(1800L))
        Assert.assertEquals("1:40:00", KtUtils.formatVideoTime(6000L))
    }


    @Test
    fun `should verify when is chinese language without`() {
        every { mContext.resources }.returns(mockk(relaxed = true) {
            every { configuration }.returns(mockk(relaxed = true) {
                every { locales }.returns(LocaleList())
            })
        })
        Assert.assertFalse(KtUtils.isChineseLanguage())

        every { mContext.resources }.returns(mockk(relaxed = true) {
            every { configuration }.returns(mockk(relaxed = true) {
                every { locales }.returns(mockk(relaxed = true) {
                    every { isEmpty }.returns(false)
                    every { get(0) }.returns(Locale("China", "CN", "110"))
                })
            })
        })
        every { mContext.applicationContext }.returns(mContext)
        MyApplication.init(mContext)
        Assert.assertTrue(KtUtils.isChineseLanguage())
    }

    @Test
    fun `should verify when get default image background without`() {

        every { mContext.applicationContext }.returns(mContext)
        every { mContext.getColor(R.color.color_text_ripple_bg_color) }.returns(111)
        MyApplication.init(mContext)

        KtUtils.getDefaultImageBackground(2.0f)
        verify {  mContext.getColor(R.color.color_text_ripple_bg_color)  }
    }

    @Test
    fun `should verify when check is OTG path without`() {
        Assert.assertFalse(KtUtils.checkIsOTGPath(null, ""))

        Assert.assertFalse(KtUtils.checkIsOTGPath(null, "test"))

        mockkObject(KtUtils)
        every { KtUtils.checkIsMultiAppPath(any()) }.returns(true)
        Assert.assertFalse(KtUtils.checkIsOTGPath(mContext, "test"))

        every { KtUtils.checkIsMultiAppPath(any()) }.returns(false)
        every { KtUtils.getStorageByPath(mContext, any()) }.returns(KtUtils.STORAGE_OTG)
        Assert.assertTrue(KtUtils.checkIsOTGPath(mContext, "test"))
    }

    @Test
    fun `should verify when check is multi app path without`() {
        mockkStatic(TextUtils::class)
        every { TextUtils.isEmpty("null") }.returns(true);
        Assert.assertFalse(KtUtils.checkIsMultiAppPath("null"))

        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsSupportMultiApp }.returns(false)
        Assert.assertFalse(KtUtils.checkIsMultiAppPath("test"))

    }

    @Test
    fun `should verify when check is SD path without`() {
        Assert.assertFalse(KtUtils.checkIsSDPath(mContext, null))
        Assert.assertFalse(KtUtils.checkIsSDPath(null, "test"))

        mockkObject(KtUtils)
        every { KtUtils.getStorageByPath(mContext, any()) }.returns(KtUtils.STORAGE_EXTERNAL)
        Assert.assertTrue(KtUtils.checkIsSDPath(mContext, "test"))
    }

    @Test
    fun `should verify when get storage by path without`() {
        Assert.assertEquals(STORAGE_INTERNAL, KtUtils.getStorageByPath(mContext, null))

        mockkObject(KtUtils)
        every { KtUtils.checkIsMultiAppPath(any()) }.returns(true)
        Assert.assertEquals(STORAGE_INTERNAL_MULTI_APP, KtUtils.getStorageByPath(mContext, "test"))

        every { KtUtils.checkIsMultiAppPath(any()) }.returns(false)

        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isSingleSdcard(mContext) }.returns(true)
        Assert.assertEquals(STORAGE_INTERNAL, KtUtils.getStorageByPath(mContext, "test"))

        every { VolumeEnvironment.isSingleSdcard(mContext) }.returns(false)

        every { VolumeEnvironment.getInternalSdPath(mContext) }.returns("t")
        Assert.assertEquals(STORAGE_INTERNAL, KtUtils.getStorageByPath(mContext, "test"))


        every { VolumeEnvironment.getInternalSdPath(mContext) }.returns("A")

        every { VolumeEnvironment.getExternalSdPath(mContext) }.returns("t")
        Assert.assertEquals(STORAGE_EXTERNAL, KtUtils.getStorageByPath(mContext, "test"))

        every { VolumeEnvironment.getExternalSdPath(mContext) }.returns("A")
        Assert.assertEquals(STORAGE_OTG, KtUtils.getStorageByPath(mContext, "test"))
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}