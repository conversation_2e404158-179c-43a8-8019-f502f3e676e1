package com.filemanager.common.base

import android.net.Uri
import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.constants.Constants
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.PathUtils
import io.mockk.every
import io.mockk.mockkStatic
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * RemoteFileBean 的单元测试类
 * 用于测试 RemoteFileBean 类的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决兼容性问题
class RemoteFileBeanTest {

    // 测试用的 RemoteFileData 对象
    private lateinit var remoteFileData: RemoteFileData

    /**
     * 在每个测试方法执行前初始化测试数据
     */
    @Before
    fun setUp() {
        // 创建一个模拟的 RemoteFileData 对象并设置初始值
        remoteFileData = RemoteFileData().apply {
            fileName = "test.jpg"
            path = "/test/path"
            size = 1024L
            createDate = 1000L
            modifyDate = 2000L
            lastOpenDate = 3000L
            isDir = false
            fileNum = 10
            remoteImageWidth = 1920
            remoteImageHeight = 1080
            remoteVideoDuration = 60000L
            remoteAudioChannel = 2
        }
    }

    /**
     * 测试 RemoteFileBean 的构造函数
     * 验证从 RemoteFileData 构造 RemoteFileBean 时属性是否正确设置
     */
    @Test
    fun testConstructorWithRemoteFileData() {
        val bean = RemoteFileBean(remoteFileData)
        
        // 验证各个属性是否正确设置
        assertEquals("test.jpg", bean.mDisplayName)
        assertEquals("/test/path", bean.originalPath)
        assertEquals(Constants.REMOTE_PATH_PREFIX + "/test/path", bean.mData)
        assertEquals(1024L, bean.mSize)
        assertEquals(2000L, bean.mDateModified)
        assertEquals(3000L, bean.lastOpenTime)
        assertFalse(bean.mIsDirectory)
        assertEquals(1000L, bean.createTime)
        assertEquals(10, bean.folderFileNum)
        assertEquals(1920, bean.remotePicWidth)
        assertEquals(1080, bean.remotePicHeight)
        assertEquals(2, bean.remoteAudioChannels)
    }

    /**
     * 测试最近文件夹路径的构造
     * 验证当路径是最近文件夹时，mData 属性是否正确构造
     */
    @Test
    fun testRecentFolderPathConstruction() {
        // 设置路径为最近文件夹路径
        remoteFileData.path = Constants.REMOTE_RECENT_DIR_PATH
        // 模拟 PathUtils.getCurrentRemoteMacRootPath() 方法返回固定值
        mockkStatic(PathUtils::class)
        every { PathUtils.getCurrentRemoteMacRootPath() } returns "user123"
        
        val bean = RemoteFileBean(remoteFileData)
        
        // 验证 mData 是否正确构造
        assertEquals(Constants.REMOTE_PATH_PREFIX + "user123/" + Constants.REMOTE_RECENT_DIR_PATH, bean.mData)
        // 验证最近文件夹使用最后打开时间作为修改时间和创建时间
        assertEquals(3000L, bean.mDateModified)
        assertEquals(3000L, bean.createTime)
    }

    /**
     * 测试 isRecentFolder 方法
     * 验证是否能正确识别最近文件夹
     */
    @Test
    fun testIsRecentFolder() {
        // 测试普通文件路径
        val normalBean = RemoteFileBean(remoteFileData)
        assertFalse(normalBean.isRecentFolder())
        
        // 测试最近文件夹路径
        remoteFileData.path = Constants.REMOTE_RECENT_DIR_PATH
        val recentBean = RemoteFileBean(remoteFileData)
        assertTrue(recentBean.isRecentFolder())
    }

    /**
     * 测试 shouldShowAlternativeName 方法
     * 验证是否应该显示替代名称
     */
    @Test
    fun testShouldShowAlternativeName() {
        // 测试没有设置替代名称的情况
        val bean1 = RemoteFileBean(remoteFileData)
        assertFalse(bean1.shouldShowAlternativeName())
        
        // 测试设置了不同替代名称的情况
        remoteFileData.alternativeName = "different_name"
        val bean2 = RemoteFileBean(remoteFileData)
        assertTrue(bean2.shouldShowAlternativeName())
        
        // 测试替代名称与显示名称相同的情况
        remoteFileData.alternativeName = remoteFileData.fileName
        val bean3 = RemoteFileBean(remoteFileData)
        assertFalse(bean3.shouldShowAlternativeName())
    }

    /**
     * 测试 getRemotePath 方法
     * 验证是否能正确返回远程路径
     */
    @Test
    fun testGetRemotePath() {
        val bean = RemoteFileBean(remoteFileData)
        assertEquals("/test/path", bean.getRemotePath())
    }

    /**
     * 测试目录类型的设置
     * 验证当文件是目录时，mLocalType 是否正确设置为目录类型
     */
    @Test
    fun testDirectoryType() {
        remoteFileData.isDir = true
        val bean = RemoteFileBean(remoteFileData)
        assertEquals(MimeTypeHelper.DIRECTORY_TYPE, bean.mLocalType)
    }

    /**
     * 测试未知文件类型的处理
     * 验证当文件类型未知时，mLocalType 是否正确设置为未知类型
     */
    @Test
    fun testUnknownFileType() {
        remoteFileData.fileName = "test.unknown"
        val bean = RemoteFileBean(remoteFileData)
        assertEquals(MimeTypeHelper.UNKNOWN_TYPE, bean.mLocalType)
    }
}