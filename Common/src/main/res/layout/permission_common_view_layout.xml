<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/permission_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    android:background="?attr/couiColorBackgroundWithCard"
    android:clickable="true"
    tools:visibility="visible"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/guide_center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guide_center">

        <include
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            layout="@layout/fragment_with_button_no_msg"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/empty_toolbar_layout"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginTop="@dimen/dimen_6dp"
        android:gravity="center_vertical"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/top_back_icon"
            android:layout_width="@dimen/permission_back_icon_size"
            android:layout_height="@dimen/permission_back_icon_size"
            android:layout_marginStart="@dimen/dimen_14dp"
            android:background="@drawable/coui_toolbar_menu_bg"
            android:padding="6dp"
            android:src="@drawable/coui_back_arrow" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textColor="@color/black"
            android:textSize="16dp"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.45" />

</androidx.constraintlayout.widget.ConstraintLayout>