plugins {
    id 'com.android.library'
    id "kotlin-parcelize"
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace 'com.oplus.filemanager.account'
}

dependencies {
    implementation libs.androidx.appcompat
    implementation libs.google.material

    // Account SDK
    implementation libs.heytap.account.uc
    implementation libs.oplus.stdid.sdk
    implementation libs.koin.android

    implementation project(":Common")
}