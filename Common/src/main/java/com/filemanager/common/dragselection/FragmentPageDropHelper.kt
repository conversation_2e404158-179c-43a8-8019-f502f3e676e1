/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/24, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.view.DragEvent
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.state.COUIMaskRippleDrawable
import com.coui.appcompat.state.COUIMaskRippleDrawable.RIPPLE_TYPE_ICON_RADIUS
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.dragselection.action.DropDispatchAction
import com.filemanager.common.dragselection.action.DropDispatchAction.NORMAL_STATE
import com.filemanager.common.dragselection.action.DropDispatchAction.PROHIBIT_STATE
import com.filemanager.common.dragselection.action.DropFolderAction
import com.filemanager.common.dragselection.util.DropHandleHelper
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.helper.CategoryHelper.CATEGORY_FOLDER
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.view.SelectDragItemViewParent
import com.filemanager.common.view.SelectItemLayout

class FragmentPageDropHelper(val context: Context) {

    companion object {
        const val TAG = "FragmentPageDropHelper"
        const val DISPERSION_ANIMATION_LIMIT_COUNT = 5
    }

    private val handleHelper = DropHandleHelper()
    private var pendingDragStartCount = 0
    private var dropResult = true
    var selectDragItemViewParent: SelectDragItemViewParent? = null
    var cornerMarkStateCode = -1
    var execAnimaton: Boolean = false
    init {
        handleHelper.dragInCallback = { view, tag ->
            if (tag?.type == DropTag.Type.TOOLBAR_MENU || tag?.type == DropTag.Type.TOOLBAR_MENU_BACK) {
                view?.let { setMenuDragIn(it, tag.type) }
            } else {
                setItemDragIn(view, context)
            }
        }
        handleHelper.dragOutCallback = { view, tag ->
            setItemDragOut(view, context)
        }
    }

    fun handleDragEvent(activity: Activity, view: View?, event: DragEvent?, isScrolling: Boolean): Boolean {
        when (event?.action) {
            DragEvent.ACTION_DROP -> {
                Log.d(TAG, "handleDragEvent -> ACTION_DROP itemDropTag ${handleHelper.itemDropTag}" +
                        " fragmentDropTag ${handleHelper.fragmentDropTag}")
                val dropTag = handleHelper.itemDropTag?.type
                DragUtils.isDragging = false
                if (dropTag == DropTag.Type.MAC_FRAGMENT_VIEW) {
                    Log.d(TAG, "$dropTag -> cannot drop in mac")
                    dropResult = false
                    execAnimation(activity, event)
                    return false
                }
                dropActionOperateFile(activity, event)
                handleHelper.resetView()
                return execAnimation(activity, event)
            }

            DragEvent.ACTION_DRAG_STARTED -> {
                if (activity is IDraggingActionOperate) {
                    DragUtils.isDragging = true
                    DragUtils.dragStartPath = activity.getDragCurrentPath()
                    DragUtils.hasAndroidDataFile = event.clipDescription?.extras
                        ?.getBoolean(CommonConstants.KEY_HAS_ANDROID_DATA_FILE) ?: false
                    activity.setNavigateItemAble()
                }
                dropResult = true
                pendingDragStartCount++
                Log.d(TAG, "handleDragEvent ACTION_DRAG_STARTED")
            }

            DragEvent.ACTION_DRAG_ENTERED -> {
                Log.d(TAG, "handleDragEvent ACTION_DRAG_ENTERED")
                DragUtils.actionDragEntered()
                handleHelper.removeCallbackTask()
            }

            DragEvent.ACTION_DRAG_LOCATION -> {
                if (pendingDragStartCount > 0) {
                    pendingDragStartCount--
                    buildAnimationLayout(event, 0, activity)
                }
                handleHelper.handleDropLocation(view, view, event, activity, isScrolling, false)
                selectDragItemViewParent?.updateTargetPosition(event.x, event.y)
                updateCornerMarkState(activity, event)
            }

            DragEvent.ACTION_DRAG_EXITED -> {
                Log.d(TAG, "handleDragEvent ACTION_DRAG_EXITED")
                setItemDragOut(handleHelper.targetItemView, context)
                handleHelper.removeCallbackTask()
                DragUtils.lastCornerMarkStateCode = NORMAL_STATE
                DragUtils.setCornerMarkStateCode(NORMAL_STATE, activity.window.decorView)
                DragUtils.actionDragExited()
                selectDragItemViewParent?.stopDragDropAnimation()
                handleHelper.resetView()
            }

            DragEvent.ACTION_DRAG_ENDED -> {
                Log.d(TAG, "handleDragEvent ACTION_DRAG_ENDED")
                dragEnd(event, activity)
            }

            else -> {}
        }
        return dropResult
    }

    private fun dragEnd(event: DragEvent, activity: Activity) {
        handleHelper.removeCallbackTask()
        if (activity is IDraggingActionOperate && !execAnimaton) {
            activity.setNavigateItemAble()
        }
        DefaultDropListener.notifyRecyclerItemAlpha(false)
        execAnimaton = false
        DragUtils.actionDragEnded(event, activity)
        DragUtils.resetAllState()
    }

    private fun dropActionOperateFile(activity: Activity, event: DragEvent?) {
        if (handleHelper.itemDropTag != null && handleHelper.itemDropTag?.isMac == false) {
            MacDragUtil.MacDragObject.targetCategoryType = CATEGORY_FOLDER
            dropResult = DropFolderAction.handleDropFolderAction(
                activity,
                event,
                handleHelper.itemDropTag?.filePath
            )
            setItemDragOut(handleHelper.targetItemView, context)
        } else if (handleHelper.fragmentDropTag != null) {
            val categoryType = handleHelper.fragmentDropTag?.categoryType ?: -1
            dropResult = DropDispatchAction.handleDragDrop(activity, categoryType, event, false)
        }
    }

    private fun updateCornerMarkState(activity: Activity, event: DragEvent?) {
        if (!DragUtils.isSingleShadowComplete || !DragUtils.isWholeShadowComplete) {
            Log.d(TAG, "updateDragShadowBadge, doing shadow animation and return")
            return
        }
        if (handleHelper.itemDropTag != null) {
            val targetItemViewTag = handleHelper.targetItemView?.let {
                (it.tag as? DropTag)
            }
            if (handleHelper.itemDropTag?.type == DropTag.Type.TOOLBAR_MENU
                || handleHelper.itemDropTag?.type == DropTag.Type.TOOLBAR_MENU_BACK
                || handleHelper.itemDropTag?.type == DropTag.Type.MAIN_TAB
            ) {
                cornerMarkStateCode = DropDispatchAction.NORMAL_STATE
            } else {
                if (targetItemViewTag?.type == DropTag.Type.ITEM_VIEW_NOTRESPONSE
                    || targetItemViewTag?.type == DropTag.Type.RECENT_TAB
                ) {
                    cornerMarkStateCode = DropDispatchAction.PROHIBIT_STATE
                } else {
                    val categoryType = targetItemViewTag?.categoryType ?: 0
                    cornerMarkStateCode =
                        DropDispatchAction.getCornerMarkStateCode(activity, categoryType, event)
                }
            }
        } else if (handleHelper.fragmentDropTag != null) {
            val categoryType = handleHelper.fragmentDropTag?.categoryType ?: -1
            Log.d(TAG, "fragmentDropTag is $categoryType")
            cornerMarkStateCode =
                DropDispatchAction.getCornerMarkStateCode(activity, categoryType, event)
        } else {
            cornerMarkStateCode = PROHIBIT_STATE
        }
        Log.d(TAG, "updateCornerMarkState current：$cornerMarkStateCode last:${DragUtils.lastCornerMarkStateCode}")
        if (cornerMarkStateCode != DragUtils.lastCornerMarkStateCode) {
            DragUtils.lastCornerMarkStateCode = cornerMarkStateCode
            DragUtils.sendCornerMarkBroadcast(cornerMarkStateCode, context)
        }
    }

    @SuppressLint("Range")
    private fun buildAnimationLayout(event: DragEvent, state: Int, activity: Activity) {
        if (UIConfigMonitor.isZoomWindowShow()) {
            val shadowBuild = DragUtils.getShadowBuild()
            shadowBuild?.apply {
                setShadowVisible(true)
                addShadowView()
            }
            activity.window.decorView.updateDragShadow(shadowBuild)
            DragUtils.lastCornerMarkStateCode = DropDispatchAction.NORMAL_STATE
            DragUtils.setCornerMarkStateCode(DropDispatchAction.NORMAL_STATE, activity.window.decorView)
            DefaultDropListener.notifyRecyclerItemAlpha(true)
            return
        }
        event.localState ?: return
        val rect = Rect()
        activity.window.decorView.getWindowVisibleDisplayFrame(rect)
        if (selectDragItemViewParent != null) {
            removeAnimationParentLayout(activity)
        }
        selectDragItemViewParent = if (state == 0) {
            SelectDragItemViewParent(context, event.x, event.y, object : SelectDragItemViewParent.IStartDragAndDrop {
                override fun startDragAndDrop() {
                    animationEndCallback(activity)
                }
            }, DefaultDropListener.viewMode)
        } else {
            SelectDragItemViewParent(context, event.x, event.y, object : SelectDragItemViewParent.IStartDragAndDrop {
                override fun startDragAndDrop() {
                    DefaultDropListener.selectedItemViews?.value?.forEach { view ->
                        view.alpha = 1f
                        view.foreground = null
                        cornerMarkStateCode = -1
                    }
                    removeAnimationParentLayout(activity)
                }
            }, DefaultDropListener.viewMode)
        }
        selectDragItemViewParent?.visibility = View.VISIBLE
        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        layoutParams.gravity = Gravity.CENTER
        val decorView = activity.window?.decorView
        if (decorView is ViewGroup) {
            decorView.addView(selectDragItemViewParent)
        }
        DefaultDropListener.selectedItemViews?.value?.let { viewList ->
            if (viewList.isNotEmpty()) {
                val shadowBuild = DragUtils.getShadowBuild()
                shadowBuild?.bitmapList?.clear()
                selectDragItemViewParent?.addTargetView(viewList, state)?.forEach { (index, view) ->
                    shadowBuild?.bitmapList?.put(index, view)
                }
            } else {
                removeAnimationParentLayout(activity)
            }
        }
    }

    private fun animationEndCallback(activity: Activity) {
        val shadowBuild = DragUtils.getShadowBuild()
        shadowBuild?.setShadowVisible(true)
        activity.window.decorView.updateDragShadow(shadowBuild)
//        shadowBuild?.startShadowAnim()
        DefaultDropListener.selectedItemViews?.value?.size?.let { size ->
            if (size > 1) {
//                shadowBuild?.startSingleShadowAnim()
            }
        }
//        removeAnimationParentLayout(activity)
        DefaultDropListener.notifyRecyclerItemAlpha(true)
    }

    private fun removeAnimationParentLayout(activity: Activity) {
        Log.d(TAG, "removeAnimationParentLayout")
        val decorView = activity.window?.decorView
        if (decorView is ViewGroup) {
            decorView.removeView(selectDragItemViewParent)
            selectDragItemViewParent = null
        }
    }

    private fun execAnimation(activity: Activity, event: DragEvent): Boolean {
        if (activity !is IDraggingActionOperate) return false
        activity.setNavigateItemAble()
        if (selectDragItemViewParent == null) {
            val selectedView = activity.getSelectedItemView()
            val selectedItemView = selectedView?.filter {
                KtViewUtils.isViewVisible(it)
            }?.toCollection(ArrayList())
            execAnimaton = true
            if (selectedItemView != null && selectedItemView.size > 0 && selectedItemView.size <= DISPERSION_ANIMATION_LIMIT_COUNT && !dropResult) {
                DefaultDropListener.selectedItemViews?.value = selectedItemView
                buildAnimationLayout(event, 1, activity)
                return true
            } else {
                if (selectedItemView.isNullOrEmpty()) {
                    DefaultDropListener.selectedItemViews?.value?.forEach { itemView ->
                        itemView.alpha = 1f
                        itemView.foreground = null
                    }
                } else {
                    selectedItemView.forEach { itemView ->
                        itemView.alpha = 1f
                        itemView.foreground = null
                    }
                }
                return dropResult
            }
        } else {
            execAnimaton = false
            selectDragItemViewParent?.cancelUpAnimation()
            return true
        }
    }

    private fun setMenuDragIn(view: View, type: DropTag.Type) {
        val maskRippleDrawable = COUIMaskRippleDrawable(view.context)
        when (type) {
            DropTag.Type.TOOLBAR_MENU -> maskRippleDrawable.setCustomRippleMask()

            DropTag.Type.TOOLBAR_MENU_BACK -> {
                maskRippleDrawable.setCircleRippleMask(COUIMaskRippleDrawable.getMaskRippleRadiusByType(view.context, RIPPLE_TYPE_ICON_RADIUS))
            }

            else -> {}
        }
        view.background = maskRippleDrawable
        COUIDarkModeUtil.setForceDarkAllow(view, false)
    }

    private fun setItemDragIn(itemView: View?, context: Context) {
        //列表
        if (itemView is SelectItemLayout) {
            itemView.isPressed = true
            return
        }

        //宫格
        itemView?.setBackgroundColor(context.resources.getColor(com.support.appcompat.R.color.coui_color_hover))
    }

    private fun setItemDragOut(itemView: View?, context: Context) {
        //列表
        if (itemView is SelectItemLayout) {
            itemView.isPressed = false
            return
        }

        //宫格
        itemView?.setBackgroundColor(context.resources.getColor(com.support.appcompat.R.color.coui_color_background_with_card))
    }
}