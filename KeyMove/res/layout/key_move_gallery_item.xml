<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:clickable="true"
    android:focusable="true">

    <com.filemanager.common.view.FileThumbView
        android:id="@+id/gallery_recycler_img_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:forceDarkAllowed="false"/>

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/gallery_recycler_checkbox_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="3dp"
        android:clickable="false"
        android:focusable="false"
        app:couiButton="@drawable/key_move_heytap_check_state"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/grid_checkbox_bg"
        android:layout_width="@dimen/grid_check_box_size"
        android:layout_height="@dimen/grid_check_box_size"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="6dp"
        android:layout_marginBottom="3dp"
        android:src="@drawable/grid_checkbox_select_none_bg"
        android:visibility="gone" />
</RelativeLayout>