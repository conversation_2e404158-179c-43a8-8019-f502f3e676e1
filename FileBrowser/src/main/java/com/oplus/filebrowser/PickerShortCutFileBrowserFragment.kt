/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/7/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser

import androidx.lifecycle.ViewModelProvider
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.view.BrowserPathBar

class PickerShortCutFileBrowserFragment : PickerFileBrowserFragment() {
    companion object {
        private const val TAG = "PickerShortCutFileBrowserFragment"
    }
    override fun createViewModel() {
        if (fragmentViewModel == null) {
            fragmentViewModel = ViewModelProvider(this)[PickerFileBrowserViewModel::class.java].apply {
                isPickerMode = true
                mPickerMultipleType = pickerManager?.getFinalPickerMimeType()
            }
        }
    }
    override fun initPathBar() {
        mPathBar?.let {
            Log.d(TAG, "initPathBar mCurrentPath = $mCurrentPath")
            val currentPath = mCurrentPath ?: return
            if (currentPath.isNotEmpty()) {
                fragmentViewModel?.initPathHelper(currentPath)
                it.setPathHelper(fragmentViewModel?.mPathHelp)
                it.setOnPathClickListener(object : BrowserPathBar.OnPathClickListener {
                    override fun onPathClick(index: Int, path: String?) {
                        fragmentViewModel?.clickPathBar(index)
                    }
                }).setTextFocusChangeListener { currentFocusText ->
                    mTitle = currentFocusText
                    if (fragmentViewModel?.mNeedScroll == true) {
                        KtAnimationUtil.showUpdateToolbarTitleWithAnimate(toolbar, mTitle)
                    } else {
                        toolbar?.title = mTitle
                    }
                }.show()
                it.setIsFromShortcutFolder(isFromShortcutFolder)
                it.updateShortcutFolderRootPath(currentPath)
                it.setCurrentPath(currentPath)
            }
        }
    }
}