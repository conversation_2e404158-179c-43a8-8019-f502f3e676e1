/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IArchiveFilePreview.kt
 * Description:
 *     The interface of previewing archive file
 *
 * Version: 1.0
 * Date: 2024-10-15
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-15   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.archive

import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.base.BaseFileBean
import com.filemanager.compresspreview.ui.CompressPreviewViewModel
import com.oplus.filemanager.preview.core.IPreviewContainerManager
import com.oplus.filemanager.preview.widget.PreviewOperationsBar

internal interface IArchiveFilePreview {

    /**
     * Attach to container views and preview archive file.
     */
    fun attachToContainer(containerManager: IArchivePreviewContainerManager)

    /**
     * Bind with [PreviewOperationsBar] to update the open button text
     */
    fun bindOperationsBar(bar: PreviewOperationsBar?)

    /**
     * Release when not use it anymore.
     */
    fun release()

    companion object {

        @JvmStatic
        fun obtain(
            lifecycleOwner: LifecycleOwner,
            previewModel: ArchivePreviewViewModel
        ): IArchiveFilePreview = ArchiveFilePreviewImpl(lifecycleOwner, previewModel)
    }
}

internal interface IArchivePreviewContainerManager : IPreviewContainerManager {
    val previewCardUi: IArchiveUiPreviewCard
    val defaultInfoUi: IArchiveUiDefaultInfo
}

/**
 * The interface to handle default info UI when preview archive file
 */
internal interface IArchiveUiDefaultInfo {

    /**
     * Whether show this UI
     */
    fun setVisible(visible: Boolean)

    /**
     * Set file info which show on UI
     */
    fun setPreviewFile(archiveFile: BaseFileBean)

    /**
     * Set notify message which show on UI
     */
    fun setNotifyMessage(msg: String?)
}

/**
 * The interface to handle preview card UI when preview archive file
 */
internal interface IArchiveUiPreviewCard {

    /**
     * Whether show this UI
     */
    fun setVisible(visible: Boolean)

    /**
     * Set file info which show on UI
     */
    fun setPreviewFile(archiveFile: BaseFileBean)

    /**
     * Set the archive file contents to show them
     */
    fun setPreviewContents(compressModel: CompressPreviewViewModel)
}