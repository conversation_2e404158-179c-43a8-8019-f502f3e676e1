/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.doc
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.doc

import android.net.Uri
import androidx.annotation.Keep
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.utils.Log
import com.filemanager.thumbnail.utils.logInfo

/**
 * construct the input object for Extracting doc's thumbnail
 */
@Keep
class DocThumbnail(
    val uri: Uri,
    val lastModified: Long,
    val size: Long,
    val docFilePath: String? = null
) {

    private companion object {
        private const val TAG = "DocThumbnail"
    }

    /**
     * 标记该缩略图是否为快捷方式的缩略图，是则会启用额外的缓存判断逻辑
     */
    var isShortcut: Boolean = false
    var extraSerializeKey: Int = 0
        set(value) {
            field = value
            hashCode = 0
        }

    private var hashCode = 0

    constructor(
        filePath: String,
        lastModified: Long,
        size: Long
    ) : this(
        uri = kotlin.runCatching {
            ThumbnailManager.getFileProviderUri(filePath)
        }.onFailure {
            Log.e(TAG, "constructor: ERROR!! $it")
        }.getOrNull() ?: Uri.EMPTY,
        lastModified = lastModified,
        size = size,
        docFilePath = filePath
    )

    override fun hashCode(): Int {
        if (hashCode == 0) {
            hashCode = uri.hashCode()
            hashCode = 31 * hashCode + lastModified.hashCode()
            hashCode = 31 * hashCode + size.hashCode()
            if (extraSerializeKey != 0) {
                hashCode = 31 * hashCode + extraSerializeKey.hashCode()
            }
        }
        return hashCode
    }

    override fun equals(other: Any?): Boolean {
        if (other is DocThumbnail) {
            return (uri == other.uri) &&
                    (lastModified == other.lastModified) &&
                    (size == other.size)
        }
        return false
    }

    override fun toString(): String {
        //重写 toString，确保 glide 中 ObjectKey 计算生成的缓存key不受 DocThumbnail 混淆后的类名影响
        return Integer.toHexString(this.hashCode())
    }

    internal fun printString(): String =
        "{uri=${uri.logInfo()}, lastModified=$lastModified, size=$size, " +
                "isShortcut=$isShortcut, extraSerializeKey=$extraSerializeKey}"
}