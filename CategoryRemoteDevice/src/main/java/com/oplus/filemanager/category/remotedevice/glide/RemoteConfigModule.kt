/*
 * ********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  RemoteConfigModule.java
 *  * * Description : RemoteConfigModule.java
 *  * * Version     : 1.0
 *  * * Date        : 19-9-20 下午4:57
 *  * * Author      : <EMAIL>
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */
package com.oplus.filemanager.category.remotedevice.glide

import android.content.Context
import android.graphics.Bitmap
import androidx.annotation.Keep
import com.bumptech.glide.Glide
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.module.LibraryGlideModule
import com.filemanager.common.base.RemoteFileBean
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.category.remotedevice.glide.thumbnail.RemoteThumbnailFactory
import com.oplus.filemanager.category.remotedevice.glide.thumbnailpath.RemoteDownloadFactory

@Keep
@GlideModule
class RemoteConfigModule : LibraryGlideModule() {
    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        super.registerComponents(context, glide, registry)

        registry.append(
            RemoteFileBean::class.java,
            Bitmap::class.java,
            RemoteThumbnailFactory(context)
        )

        registry.append(
            DownloadRemoteFileBean::class.java,
            Bitmap::class.java,
            RemoteDownloadFactory(context)
        )
    }
}
