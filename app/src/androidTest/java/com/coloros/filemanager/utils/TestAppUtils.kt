/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : TestAppUtils
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/02       1.0      create
 **********************************************************************/
package com.coloros.filemanager.utils

import android.app.Activity
import android.content.ComponentName
import android.content.Intent
import androidx.test.rule.ActivityTestRule
import com.coloros.filemanager.utils.TestPermissionUtils.applyAppListPermission
import org.junit.Rule

object TestAppUtils {
    private val TAG = TestAppUtils::class.java.simpleName

    /**
     * 测试初始化，唤醒屏幕，进入手机主屏幕。
     * 亦可在这里初始化三方插件，如Phenix等
     * @return
     * @throws InterruptedException
     */
    fun initTest() {
        //屏幕是否黑屏（完全变黑那种，屏幕变暗不算）, 黑屏且没有交互
        if (!TestPowerManagerUtils.isInteractive) {
            TestUiViewUtils.screenWakeUp()
        }
    }

    @Rule
    @Throws(Exception::class)
    inline fun <reified T : Activity> openActivity(t: Class<T>): ActivityTestRule<out T> {
        val activityTestRule = ActivityTestRule(t)
        activityTestRule.launchActivity(null)
        applyAppListPermission()
        return activityTestRule
    }

    /**
     * 打开三方app的某个Activity
     * @param activity
     * @param pkgName
     * @param launchActivityName
     * @throws InterruptedException
     */
    @Throws(InterruptedException::class)
    fun startOtherApp(activity: Activity, pkgName: String, launchActivityName: String) {
        val componentName = ComponentName(pkgName, launchActivityName)
        val intent = Intent()
        intent.component = componentName
        intent.action = Intent.ACTION_MAIN
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        activity.startActivity(intent)
        Thread.sleep(TestConstants.TIME_WAIT_ONE_SEC)
    }


    /**
     * 用于打印列表数据
     * @param list
     * @return string
     */
    fun listToString(list: List<String?>): String {
        val sb = StringBuffer()
        val listSize = list.size
        for (i in 0 until listSize) {
            if (i == listSize - 1) {
                sb.append(list[i])
            } else {
                sb.append(list[i])
                sb.append(';')
            }
        }
        return sb.toString()
    }
}