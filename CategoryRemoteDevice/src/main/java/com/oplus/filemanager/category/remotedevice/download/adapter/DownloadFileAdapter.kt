/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadFileAdapter
 * * Description : 多个下载文件的Adapter
 * * Version     : 1.0
 * * Date        : 2024/12/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.isActivityAndInvalid
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean

class DownloadFileAdapter(val context: Context, val data: List<DownloadRemoteFileBean>, val totalSize: String) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "DownloadFileAdapter"
        private const val TYPE_TOTAL_TITLE = 0
        private const val TYPE_FILE_ITEM = 1
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (TYPE_TOTAL_TITLE == viewType) {
            val itemView = LayoutInflater.from(parent.context).inflate(MultiDownloadTotalTitleVH.layoutId(), parent, false)
            MultiDownloadTotalTitleVH(itemView)
        } else {
            val itemView = LayoutInflater.from(parent.context).inflate(MultiDownloadFileVH.layoutId(), parent, false)
            MultiDownloadFileVH(itemView)
        }
    }

    override fun getItemCount(): Int {
        return data.size + 1
    }

    override fun getItemViewType(position: Int): Int {
        if (position == 0) {
            return TYPE_TOTAL_TITLE
        }
        return TYPE_FILE_ITEM
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (context.isActivityAndInvalid()) {
            Log.e(TAG, "onBindVH: activity is destroy")
            return
        }
        if (position < 0 || position >= itemCount) {
            Log.e(TAG, "position is error, return")
            return
        }

        if (holder is MultiDownloadTotalTitleVH) {
            holder.loadData(context, data.size, totalSize)
        } else if (holder is MultiDownloadFileVH) {
            val file = data.get(position - 1)
            holder.loadData(context, getItemId(position).toInt(), position, file, itemCount)
        }
    }

    override fun getItemId(position: Int): Long {
        if (position < 0 || position >= itemCount) {
            Log.e(TAG, "getItemId position is error, return")
            return RecyclerView.NO_ID
        }
        if (position == 0) {
            return 0
        }
        val realPosition = position - 1
        val path = data.get(realPosition).originalPath
        return path.hashCode().toLong()
    }
}