/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:FileDetailObserver.kt
 * * Description:
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.detail

import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.content.res.Configuration
import android.os.Handler
import android.os.Looper
import android.os.Parcel
import android.os.Parcelable
import android.view.ContextThemeWrapper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.R

open class FileDetailObserver(
    context: ContextThemeWrapper,
    private val categoryType: Int? = null
) : BaseFileActionObserver(context) {
    companion object {
        const val SHOW_DETAIL_DIALOG = 0
        const val FILE_NOT_EXISTS = 1
        private const val TIME_DELAY = 300L
    }

    private var mDetailDialog: FileDetailDialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        when (result.first) {
            SHOW_DETAIL_DIALOG -> {
                if (result.second is FileDetailBean) {
                    val detailBean = result.second as FileDetailBean
                    createOrUpdateDetailDialog(context, detailBean)
                    mDetailDialog?.show()
                }
            }
            FILE_NOT_EXISTS -> CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
            ACTION_DONE, ACTION_CANCELLED -> {
                mDetailDialog?.dismiss()
                return false
            }
        }
        return true
    }

    private fun createOrUpdateDetailDialog(context: Context, detailBean: FileDetailBean) {
        if (mDetailDialog == null) {
            mDetailDialog = FileDetailDialog(context, categoryType)
            mDetailDialog?.create()
        }
        mDetailDialog!!.updateDetail(detailBean)
    }

    @SuppressLint("ParcelCreator")
    data class FileDetailBean(var type: Int = TYPE_FILE) : Parcelable {
        // This param not be null only if has one file
        var mIsRecycleBin = false
        var mDisplayName: String? = null
        var mPath: String? = null
        var mDateModified: Long = 0
        var mRecycelDate: Long = 0L
        var mOriginPath: String? = null
        var mCalculating = true
        var mFileCount = 0
        var mDirCount = 0
        var mTotalFileSize: Long = 0
        var mLabelIds = arrayListOf<Long>()
        var mLabelNames = arrayListOf<String>()
        var mDismissListener: DialogInterface.OnDismissListener? = null
        var isShow: Boolean = false

        fun setSingleFile(singleFile: BaseFileBean?) {
            singleFile?.let {
                mDateModified = it.mDateModified
                mDisplayName = it.mDisplayName
                mPath = it.mData
                if (mDateModified == 0L) {
                    Log.d(TAG, "mDateModified is 0")
                    mDateModified = FileTimeUtil.getFileTime(mPath) ?: 0
                }
                if (it is RecycleFileWrapper) {
                    mRecycelDate = it.mRecycelDate
                    mOriginPath = it.mOriginPath
                }
            }
        }

        fun isHasLabels(): Boolean {
            return mLabelIds.size > 0
        }

        constructor(parcel: Parcel) : this(parcel.readInt()) {
            mIsRecycleBin = parcel.readByte() != 0.toByte()
            mDisplayName = parcel.readString()
            mPath = parcel.readString()
            mDateModified = parcel.readLong()
            mRecycelDate = parcel.readLong()
            mOriginPath = parcel.readString()
            mCalculating = parcel.readByte() != 0.toByte()
            mFileCount = parcel.readInt()
            mDirCount = parcel.readInt()
            mTotalFileSize = parcel.readLong()
            /*mLabelIds = parcel.readString()
            mLabelNames = parcel.readString()*/
        }

        companion object CREATOR : Parcelable.Creator<FileDetailBean> {
            const val TYPE_FILE = 0
            const val TYPE_DIRECTORY = 1
            const val TYPE_MULTI_FILES = 2
            override fun createFromParcel(parcel: Parcel): FileDetailBean {
                return FileDetailBean(parcel)
            }

            override fun newArray(size: Int): Array<FileDetailBean?> {
                return arrayOfNulls(size)
            }
        }

        fun recycle() {
            mDismissListener = null
        }

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeInt(type)
            parcel.writeByte(if (mIsRecycleBin) 1 else 0)
            parcel.writeString(mDisplayName)
            parcel.writeString(mPath)
            parcel.writeLong(mDateModified)
            parcel.writeLong(mRecycelDate)
            parcel.writeString(mOriginPath)
            parcel.writeByte(if (mCalculating) 1 else 0)
            parcel.writeInt(mFileCount)
            parcel.writeInt(mDirCount)
            parcel.writeLong(mTotalFileSize)
            /*parcel.writeString(mLabelIds)
            parcel.writeString(mLabelNames)*/
        }

        override fun describeContents(): Int {
            return 0
        }
    }

    fun onConfigurationChanged(newConfig: Configuration?) {
        if (newConfig == null) return
        Log.d(TAG, "onConfigurationChanged")
        handler.postDelayed({
            mDetailDialog?.updateLayoutWhileConfigChange(newConfig)
        }, TIME_DELAY)
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || mDetailDialog?.isShow() ?: false
    }

    fun release() {
        handler.removeCallbacksAndMessages(null)
    }
}