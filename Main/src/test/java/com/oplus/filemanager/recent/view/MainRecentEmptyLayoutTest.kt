/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.controller.FileEmptyController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/29
 * * Author      : shuqi
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *     shuqi             022/6/29     1.0       FileEmptyController unit test
 ***********************************************************************/
package com.oplus.filemanager.recent.view

import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class MainRecentEmptyLayoutTest {

    @Test
    fun initViewTest() {
        val mainRecentEmptyLayout = mockk<MainRecentEmptyLayout>(relaxed = true)
        mainRecentEmptyLayout.initView()
        Assert.assertEquals(true, mainRecentEmptyLayout.mEmptyImageView != null)
        Assert.assertEquals(true, mainRecentEmptyLayout.mEmptyTv != null)
    }
}