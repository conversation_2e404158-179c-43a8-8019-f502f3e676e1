/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptTest.kt
 * Description:
 * The test cases for Script
 *
 * Version: 1.0
 * Date: 2024-06-11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-11   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine

import org.junit.Assert
import org.junit.Test

class ScriptTest : Assert() {

    @Test
    fun `should not negative when Skill stepDelayTime if origin is negative`() {
        // Given
        val skill = Skill(stepDelayTime = -1)

        // When & Then
        assertTrue(skill.stepDelayTime() >= 0)
    }

    @Test
    fun `should equals when Skill stepDelayTime if origin is not negative`() {
        // Given
        val skill = Skill(stepDelayTime = 10)

        // When & Then
        assertEquals(10, skill.stepDelayTime())
    }

    @Test
    fun `should not negative when Action clickGestureDelay if origin is negative`() {
        // Given
        val action = Action(clickGestureDelay = -1)

        // When & Then
        assertTrue(action.clickGestureDelay() >= 0)
    }

    @Test
    fun `should equals when Action clickGestureDelay if origin is not negative`() {
        // Given
        val action = Action(clickGestureDelay = 10)

        // When & Then
        assertEquals(10, action.clickGestureDelay())
    }

    @Test
    fun `should not negative when Action clickGestureWait if origin is negative`() {
        // Given
        val action = Action(clickGestureWait = -1)

        // When & Then
        assertTrue(action.clickGestureWait() >= 0)
    }

    @Test
    fun `should equals when Action clickGestureWait if origin is not negative`() {
        // Given
        val action = Action(clickGestureWait = 10)

        // When & Then
        assertEquals(10, action.clickGestureWait())
    }

    @Test
    fun `should not negative when Action scrollParentDelay if origin is negative`() {
        // Given
        val action = Action(scrollParentDelay = -1)

        // When & Then
        assertTrue(action.scrollParentDelay() >= 0)
    }

    @Test
    fun `should equals when Action scrollParentDelay if origin is not negative`() {
        // Given
        val action = Action(scrollParentDelay = 10)

        // When & Then
        assertEquals(10, action.scrollParentDelay())
    }

    @Test
    fun `should not negative when Action handleTime if origin is negative`() {
        // Given
        val action = Action(handleTime = -1)

        // When & Then
        assertTrue(action.handleTime() >= 0)
    }

    @Test
    fun `should equals when Action handleTime if origin is not negative`() {
        // Given
        val action = Action(handleTime = 10)

        // When & Then
        assertEquals(10, action.handleTime())
    }

    @Test
    fun `should not negative when Action waitTime if origin is negative`() {
        // Given
        val action = Action(waitTime = -1)

        // When & Then
        assertTrue(action.waitTime() >= 0)
    }

    @Test
    fun `should equals when Action waitTime if origin is not negative`() {
        // Given
        val action = Action(waitTime = 10)

        // When & Then
        assertEquals(10, action.waitTime())
    }
}