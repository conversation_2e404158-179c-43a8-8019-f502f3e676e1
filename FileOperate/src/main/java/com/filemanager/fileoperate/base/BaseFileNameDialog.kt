/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: BaseFileNameDialog.kt
 ** Description: A file name input dialog, implemented the following checking: <br>
 *     1. Empty file name<br>
 *     2. Illegal file name<br>
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.base

import android.content.Context
import android.content.DialogInterface
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.OnLifecycleEvent
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.edittext.COUIEditText
import com.coui.appcompat.edittext.COUIInputView
import com.filemanager.common.constants.CommonConstants.CUSTOM_NAME_LEN
import com.filemanager.common.constants.CommonConstants.NAME_BYTES_LEN
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.RenameErrorTipUtil
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.noMoreAction
import java.nio.charset.StandardCharsets
import java.util.regex.Pattern

abstract class BaseFileNameDialog(var mContext: Context) : TextWatcher, LifecycleObserver {
    companion object {
        private const val TAG = "BaseFileNameDialog"

        //^\..*|.*[\\/*:?<>|"]+?.*|.*\.+$
        //Remove regular expression judgments beginning and ending with dots
        const val FILE_NAME_FINAL_REGEX = ".*[\\\\/*:?<>|\"]+?.*"

        /** 屏幕最小高度，小于此高度，不显示子标题 */
        private const val DIALOG_MIN_SCREEN_HEIGHT = 300
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    var mInputView: COUIInputView? = null

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    var mDialog: AlertDialog? = null
        get() = field.noMoreAction()
        set(value) { field = value.noMoreAction() }
    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    var mDialogBuilder: COUIAlertDialogBuilder? = null
        get() = field.noMoreAction()
        set(value) { field = value.noMoreAction() }

    @VisibleForTesting
    var mReShowDialogInputText: String? = null
        get() = field.noMoreAction()
        set(value) {
            field = value.noMoreAction()
        }
    @VisibleForTesting
    var mOppoEditText: EditText? = null
        get() = field.noMoreAction()

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    val mIllegalFilter = InputFilter { source, _, _, dest, dstart, dend ->
        if (EmojiUtils.containsIllegalCharFileName(source)) {
            showNotice(RenameErrorTipUtil.ERROR_FILENAME_INPUT_ILLEGAL)
            if (dest != null) {
                return@InputFilter dest.subSequence(dstart, dend)
            }
        } else if (EmojiUtils.isContainEmoji(source)) {
            showNotice(RenameErrorTipUtil.ERROR_FILENAME_ILLEGAL)
            if (dest != null) {
                return@InputFilter dest.subSequence(dstart, dend)
            }
        }
        source
    }
        get() = field.noMoreAction()

    private var mInitFileNameLen = 0

    private val mFileNamePattern = Pattern.compile(FILE_NAME_FINAL_REGEX)
        get() = field.noMoreAction()

    init {
        (mContext as? LifecycleOwner)?.let {
            it.lifecycle.addObserver(this)
        }
    }

    open fun show() {}

    open fun setMaxCount() {
        mInputView?.maxCount = getNameLengthLimit()
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    fun initViews(clickPositive: () -> Unit) {
        mInputView = mDialog?.findViewById(com.filemanager.common.R.id.inputViewDialogFileName)
        mOppoEditText = mInputView?.editText
        mOppoEditText?.apply {
            showSoftInput(this)
            addInputFilter(this, mIllegalFilter)
            ViewHelper.setClassificationTextSize(context, this)
            addTextChangedListener(this@BaseFileNameDialog)
        }
        setMaxCount()

        mInputView?.editText?.addOnErrorStateChangedListener(object : COUIEditText.OnErrorStateChangedListener {
            override fun onErrorStateChanged(errorState: Boolean) {
                Log.d(TAG, "onErrorStateChanged errorState = $errorState")
            }

            override fun onErrorStateChangeAnimationEnd(errorState: Boolean) {
                mInputView?.editText?.setSelection(mInputView?.editText?.length()!!)
            }
        })

        mDialog?.getButton(AlertDialog.BUTTON_POSITIVE)?.setOnClickListener {
            if (checkFileNameIllegal(assembleFileName())) {
                showNotice(RenameErrorTipUtil.ERROR_FILENAME_ILLEGAL)
            } else {
                clickPositive()
            }
        }
        postExecuteSetWeight()
    }

    @VisibleForTesting
    fun postExecuteSetWeight() {
        mDialog?.window?.decorView?.post { adjustWeight() }
    }

    private fun adjustWeight() {
        mDialog?.takeIf { it.isShowing && (it.window != null) }?.apply {
            val height = if (SdkUtils.isAtLeastR()) {
                window?.windowManager?.currentWindowMetrics?.bounds?.height() ?: 0
            } else {
                window?.windowManager?.defaultDisplay?.height ?: 0
            }
            val heightScreenDp = ViewHelper.px2dip(mContext, height)
            val messageView = findViewById<TextView>(android.R.id.message)
            val hasMessage = (messageView is TextView) && (!TextUtils.isEmpty(messageView.text))
            if (heightScreenDp < DIALOG_MIN_SCREEN_HEIGHT) {
                findViewById<View>(androidx.appcompat.R.id.scrollView)?.setWeight(1f)
                findViewById<View>(androidx.appcompat.R.id.customPanel)?.setWeight(1f)
                if (hasMessage) {
                    findViewById<View>(androidx.appcompat.R.id.contentPanel)?.visibility = View.GONE
                }
            } else {
                findViewById<View>(androidx.appcompat.R.id.scrollView)?.setWeight(0f)
                findViewById<View>(androidx.appcompat.R.id.customPanel)?.setWeight(0f)
                if (hasMessage) {
                    findViewById<View>(androidx.appcompat.R.id.contentPanel)?.visibility = View.VISIBLE
                }
            }
        }
    }

    private fun View.setWeight(weight: Float) {
        layoutParams = layoutParams.let {
            (it as? LinearLayout.LayoutParams)?.weight = weight
            (it as? LinearLayoutCompat.LayoutParams)?.weight = weight
            it
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun onActivityResume() {
        mOppoEditText?.let { showSoftInput(it) }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    fun setPositiveButtonEnabled(enable: Boolean) {
        mDialog?.getButton(DialogInterface.BUTTON_POSITIVE)?.isEnabled = enable
    }

    protected fun setInitFileNameInfo(nameLen: Int) {
        mInitFileNameLen = nameLen
    }

    override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
    }

    override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        if (count > 0) {
            val charsLen = s.toString().length
            val bytesLen = s.toString().toByteArray(StandardCharsets.UTF_8).size
            Log.d(TAG, "onTextChanged charLen = $charsLen, bytesLen = $bytesLen")
            handleInputLimit(charsLen, bytesLen, mInitFileNameLen) {
                val inputCharLen = mOppoEditText?.text?.length ?: 0
                if (((start + count) <= inputCharLen) && (count >= before)) {
                    mOppoEditText?.text?.delete(start + before, start + count)
                }
            }
        }
        setPositiveButtonEnabled(s.isNotEmpty())
    }

    override fun afterTextChanged(editable: Editable) {
    }

    /**
     * When inputting, judge whether to limit according to the length of input characters.
     * There are two conditions for judging whether to restrict:
     * 1. The number of input characters exceeds 50,
     *  and the number of initial characters does not exceed 50;
     * 2. The number of input characters bytes exceeds 255,
     *  and the number of initial characters bytes does not exceed 255.
     *
     * @param charsLen the number of character
     * @param bytesLen the number of character bytes
     * @param initCharsLen Initial character length
     * @param limitOperate the operate when input character exceed limit
     */
    private fun handleInputLimit(charsLen: Int, bytesLen: Int, initCharsLen: Int, limitOperate: () -> Unit) {
        val charLenExceedLimit = (initCharsLen <= getNameLengthLimit()) && (charsLen > getNameLengthLimit())
        val charByteLenExceedLimit = (initCharsLen >= getNameLengthLimit()) && (bytesLen > getNameByteLengthLimit())
        if (charLenExceedLimit || charByteLenExceedLimit) {
            limitOperate()
            showNotice(RenameErrorTipUtil.ERROR_FILE_NAME_TOO_LONG)
        }
    }

    open fun getNameLengthLimit(): Int = CUSTOM_NAME_LEN
    open fun getNameByteLengthLimit(): Int = NAME_BYTES_LEN

    protected fun getInputValue(): String {
        return mOppoEditText?.text?.toString()?.trim()?.replace("\n", "")?.replace("\r", "") ?: ""
    }

    @VisibleForTesting
    fun checkFileNameIllegal(name: String): Boolean {
        if ((name == ".") || (name == "..")) {
            return true
        }
        return mFileNamePattern.matcher(name).matches()
    }

    protected open fun assembleFileName() = getInputValue()

    private fun showNotice(type: Int) {
        val resources = mContext.resources ?: return
        val notice = RenameErrorTipUtil.getRenameErrorTips(type, resources)
        mInputView?.post { mInputView?.showError(notice) }
    }

    @VisibleForTesting
    fun addInputFilter(editText: EditText, inputFilter: InputFilter) {
        val filters: Array<InputFilter> = editText.filters
        var tmpFilter: Array<InputFilter?>? = null
        var length = 0
        if (filters == null) {
            tmpFilter = arrayOfNulls(1)
        } else {
            length = filters.size
            tmpFilter = arrayOfNulls(length + 1)
            System.arraycopy(filters, 0, tmpFilter, 0, length)
        }
        tmpFilter[length] = inputFilter
        editText.filters = tmpFilter
    }

    @Suppress("TooGenericExceptionCaught")
    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    fun hideSoftInput(editText: EditText) {
        val imm = editText.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(editText.windowToken, 0)
    }

    @VisibleForTesting
    fun showSoftInput(editText: EditText) {
        editText.isFocusable = true
        editText.isFocusableInTouchMode = true
        editText.requestFocus()
        mDialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
    }

    @Suppress("TooGenericExceptionCaught")
    open fun dismiss() {
        try {
            mDialog?.dismiss()
        } catch (e: Exception) {
            Log.w(TAG, "Failed to dismiss dialog: ${e.message}")
        }
        (mContext as? LifecycleOwner)?.let {
            it.lifecycle.removeObserver(this)
        }
    }

    fun isShowing() = mDialog?.isShowing

    fun reShowDialog() {
        val isBottomAlertDialogShowing = mDialog?.isShowing ?: false
        if (isBottomAlertDialogShowing && mDialogBuilder != null) {
            mDialog?.window?.setWindowAnimations(0)
            mReShowDialogInputText = mOppoEditText?.text?.toString()
            mDialog?.window?.decorView?.post {
                mDialog?.dismiss()
                show()
            }
        }
    }

    /**
     * 检测是否已有输入，当reShowDialog的时候记录输入的值，重新弹起时直接赋值
     * @return 已有输入，返回true
     */
    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    fun checkInputText(): Boolean {
        if (mReShowDialogInputText != null) {
            mOppoEditText?.setText(mReShowDialogInputText)
            mReShowDialogInputText = null
            return true
        }
        return false
    }

    interface OnButtonClickListener {
        fun onClick(dialog: AlertDialog, buttonId: Int = DialogInterface.BUTTON_NEGATIVE, inputValue: String? = null)
    }
}