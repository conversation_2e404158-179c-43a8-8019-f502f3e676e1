<?xml version="1.0" encoding="utf-8"?>
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

  <item
      android:id="@+id/animate_on"
      android:drawable="@drawable/line_arrow_expanded"
      app:supportExpandedAnimate="true"/>

  <item
      android:id="@+id/animate_off"
      android:drawable="@drawable/line_arrow_collapsed"
      app:supportCollapsedAnimate="true"/>

  <item
      android:id="@+id/on"
      android:drawable="@drawable/line_arrow_expanded"
      app:supportExpanded="true"/>

  <item
      android:id="@+id/off"
      android:drawable="@drawable/line_arrow_collapsed"
      app:supportCollapsed="true"/>

  <item
      android:id="@+id/defaults_off"
      android:drawable="@drawable/line_arrow_collapsed"/>

  <transition
      android:drawable="@drawable/line_arrow_collapsed_anim"
      android:fromId="@id/animate_on"
      android:toId="@id/animate_off"/>

  <transition
      android:drawable="@drawable/line_arrow_expanded_anim"
      android:fromId="@id/animate_off"
      android:toId="@id/animate_on"/>

  <transition
      android:drawable="@drawable/line_arrow_collapsed_anim"
      android:fromId="@id/on"
      android:toId="@id/animate_off"/>

  <transition
      android:drawable="@drawable/line_arrow_expanded_anim"
      android:fromId="@id/off"
      android:toId="@id/animate_on"/>

  <transition
      android:drawable="@drawable/line_arrow_expanded_anim"
      android:fromId="@id/defaults_off"
      android:toId="@id/animate_on"/>
</animated-selector>

