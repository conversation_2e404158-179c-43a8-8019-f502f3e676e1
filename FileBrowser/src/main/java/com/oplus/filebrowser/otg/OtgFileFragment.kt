/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.filebrowser.otg
 * * Version     : 1.0
 * * Date        : 2020/5/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser.otg

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.annotation.IdRes
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.responsiveui.config.UIConfig.WindowType
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter.Companion.FILE_BROWSER_FOLDER_ANIM_DELAY
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_NORMAL_MODE
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.AddFileClickEvent
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.IRefreshFragmentDataForDir
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.HighlightUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar
import com.filemanager.common.view.BrowserPathBar.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import com.filemanager.common.view.FeedbackFloatingButton
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filebrowser.R
import com.oplus.filebrowser.adapter.FileBrowserAdapter
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class OtgFileFragment : RecyclerSelectionVMFragment<SelectFileBrowserViewModel>(), OnBackPressed,
        NavigationBarView.OnItemSelectedListener, IPreviewListFragment, IRefreshFragmentDataForDir {

    companion object {
        private const val TAG = "OtgFileFragment"
        private const val NOTIFY_CHANGED_DELAY = 300L
        private const val LOADING_DELAY_TIME = 500L
    }

    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var mToolbar: COUIToolbar? = null
    private var sortEntryView: SortEntryView? = null
    private var mTitle: String? = null
    private var mCurrentPath: String? = null
    private var mAdapter: FileBrowserAdapter? = null
    private var mLayoutManager: FileGridLayoutManager? = null
    private var mPathBar: BrowserPathBar? = null
    private var createFolderFab: FeedbackFloatingButton? = null
    private var mIsSetInitLoad = false
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private var mLoadingController: LoadingController? = null
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER)
    }
    private var isFromDetail = false
    private var isFromOTGList = false
    private var isChildDisplay = false
    private var mNeedLoadData = false
    private var hasShowEmpty: Boolean = false
    private var scrollHelper: DragScrollHelper? = null

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }

    private var mFileOperateController: NormalFileOperateController? = null

    private var previewOperate: IPreviewOperate? = null

    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    override fun getLayoutResId(): Int {
        return R.layout.filebrowser_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mCurrentPath = bundle.getString(KtConstants.P_CURRENT_PATH)
            mTitle = bundle.getString(KtConstants.P_TITLE)
            mIsSetInitLoad = bundle.getBoolean(KtConstants.P_INIT_LOAD, false)
            mAdapter = FileBrowserAdapter(it, <EMAIL>)
            mAdapter!!.setHasStableIds(true)
            isFromDetail = bundle.getBoolean(KtConstants.FROM_DETAIL)
            isFromOTGList = bundle.getBoolean(KtConstants.FROM_OTG_LIST)
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
        }
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        appBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        mPathBar = view.findViewById(com.oplus.selectdir.R.id.path_bar)
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        toolbar = mToolbar

        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        mGridSpanAnimationHelper = GridSpanAnimationHelper(fragmentRecyclerView!!)
        initToolbar()
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getBrowserKey(mCurrentPath))
        sortEntryView?.setClickSortListener {
            performClickMenuEvent(R.id.navigation_sort)
        }
        initCreateFolderFab(view)
        scrollHelper = DragScrollHelper(getRecyclerView())
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    private fun initCreateFolderFab(view: View) {
        createFolderFab = view.findViewById(R.id.add_folder_fab)
        updateFloatButtonMargin()
        createFolderFab?.setFloatingButtonClickListener {
            performClickMenuEvent(R.id.navigation_new_folder)
        }
    }

    /**
     * 触发menu的点击事件
     */
    @SuppressLint("RestrictedApi")
    private fun performClickMenuEvent(@IdRes menuId: Int) {
        val activity = baseVMActivity ?: return
        val menu = ActionMenuItem(activity, 0, menuId, 0, 0, "")
        onMenuItemSelected(menu)
    }

    private fun initToolbar() {
        mToolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(R.menu.file_browser_menu)
            setToolbarMenuVisible(this, !isChildDisplay)
        }
        if (previewOperate?.isSupportPreview() != true) {
            rootView?.apply {
                setPadding(paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
            }
        }
        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.let {
                setNormalHomeDisplay(mCurrentPath)
            }
        }
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
        toolbar.menu.findItem(R.id.action_add)?.isVisible = false
    }

    private fun updateEditAndSortMenu(toolbar: COUIToolbar) {
        val edit = toolbar.menu.findItem(R.id.actionbar_edit)
        edit?.isVisible = fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setNormalHomeDisplay(fragmentViewModel?.mPositionModel?.value?.mCurrentPath)
        }
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = mTitle
        toolbar.inflateMenu(R.menu.file_browser_menu)

        updateEditAndSortMenu(toolbar)
        setToolbarMenuVisible(toolbar, !isChildDisplay)
        previewOperate?.onToolbarMenuUpdated(toolbar.menu)
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    override fun createViewModel(): SelectFileBrowserViewModel {
        val vm = ViewModelProvider(this)[SelectFileBrowserViewModel::class.java]
        val sortMode = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getBrowserKey(mCurrentPath))
        mFileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_OTG_BROWSER, vm, sortMode).also {
            it.setResultListener(FileOperatorListenerImpl(vm, false))
        }
        return vm
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentRecyclerView?.let {
            mLayoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType = mAdapter?.getItemViewType(position)
                        val isSingleLine = (viewType == BaseFileBean.TYPE_FILE_LIST_HEADER) || (viewType == BaseFileBean.TYPE_LABEL_FILE)
                        return if (isSingleLine) spanCount else 1
                    }
                }
            }
            it.addItemDecoration(mSpacesItemDecoration)
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            it.layoutManager = mLayoutManager!!
            it.itemAnimator = mFolderTransformAnimator
            it.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            mAdapter?.let { adapter ->
                fragmentRecyclerView!!.adapter = adapter
            }
            mToolbar?.post {
                if (isAdded) {
                    val paddingBottom = if (it.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        it.paddingBottom
                    }
                    it.setPadding(it.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(appBarLayout), it.paddingRight, paddingBottom)
                }
            }
            it.setLoadStateForScroll(this)
        }
        initPathBar()
        if (mNeedLoadData) {
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    private fun initPathBar() {
        mPathBar?.let {
            if (!TextUtils.isEmpty(mCurrentPath)) {
                fragmentViewModel?.initPathHelper(mCurrentPath!!)
                it.setPathHelper(fragmentViewModel?.mPathHelp)
                it.setOnPathClickListener(object : BrowserPathBar.OnPathClickListener {
                    override fun onPathClick(index: Int, path: String?) {
                        fragmentViewModel?.clickPathBar(index)
                    }
                }).setTextFocusChangeListener(object : BrowserPathBar.OnTextFocusColorChangeListener {
                    override fun onFocusChange(currentFocusText: String) {
                        mTitle = currentFocusText
                        if (fragmentViewModel?.mNeedScroll == true) {
                            KtAnimationUtil.showUpdateToolbarTitleWithAnimate(mToolbar, mTitle)
                        } else {
                            mToolbar?.title = mTitle
                        }
                    }
                }).show()
                it.setCurrentPath(mCurrentPath!!)
            }
        }

    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            viewModule.mModeState.listModel.observe(this) { listModel ->
                onListModelChanged(viewModule, listModel)
            }
            viewModule.uiState.observe(this) { fileUiModel ->
                onFileUiModelChanged(fileUiModel, viewModule)
            }
            viewModule.mPositionModel.observe(this) { positionModel ->
                onPositionModelChanged(viewModule, positionModel)
            }
            startScanModeObserver()
            startObserveLoadState()
            startCreateFolderObserve()
            startSideNavigationStatusObserver()
            fragmentViewModel?.previewClickedFileLiveData?.observe(this) {
                mAdapter?.setPreviewClickedFile(it)
            }
        }
    }

    private fun onListModelChanged(
        viewModule: SelectFileBrowserViewModel,
        value: Int
    ) {
        if (!viewModule.mModeState.initState) {
            mToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            return
        }
        Log.d(TAG, "listModel=$value")
        if (value == KtConstants.LIST_SELECTED_MODE) {
            HighlightUtil.endAnimation()
            (baseVMActivity as? NavigationInterface)?.apply {
                showNavigation()
            }
            mAdapter?.let {
                it.setSelectEnabled(true)
                it.setChoiceModeAnimFlag(true)
            }
            previewEditedFiles(fragmentViewModel?.getSelectItems())
            fragmentRecyclerView?.let {
                val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                it.setPadding(
                    it.paddingLeft,
                    it.paddingTop,
                    it.paddingRight,
                    KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
                )
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarWithEditMode(it)
                    refreshSelectToolbar(it)
                })
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            createFolderFab?.visibility = View.GONE
        } else {
            previewClickedFile(fragmentViewModel?.previewClickedFileLiveData?.value, fragmentViewModel?.previewClickedFileLiveData)
            mAdapter?.let {
                it.setSelectEnabled(false)
                it.setChoiceModeAnimFlag(false)
            }
            fragmentRecyclerView?.let {
                it.setPadding(
                    it.paddingLeft, it.paddingTop, it.paddingRight,
                    appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                )
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarNormalMode(it)
                    refreshScanModeItemIcon(it)
                }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            (baseVMActivity as? NavigationInterface)?.apply {
                hideNavigation()
            }
            createFolderFab?.visibility = View.VISIBLE
        }
    }

    private fun onFileUiModelChanged(
        fileUiModel: SelectFileBrowserViewModel.FileBrowserUiModel,
        viewModule: SelectFileBrowserViewModel
    ) {
        Log.d(
            TAG, "SuperListUiModel uiState =" + fileUiModel.fileList.size + ","
                    + fileUiModel.selectedList.size + "," + fileUiModel.keyWord
        )
        sortEntryView?.setFileCount(viewModule.getRealFileSize())
        HighlightUtil.cancelAnimation()
        if (fileUiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
            mToolbar?.let(::refreshSelectToolbar)
            if (fileUiModel.fileList is ArrayList<BaseFileBean>) {
                mFolderTransformAnimator.mIsFolderInAnimation = viewModule.mIsFolderIn
                mAdapter?.setData(
                    fileUiModel.fileList as ArrayList<BaseFileBean>,
                    fileUiModel.selectedList,
                    viewModule.mNeedScroll
                )
                previewEditedFiles(fragmentViewModel?.getSelectItems())
            }
            createFolderFab?.visibility = View.GONE
        } else {
            previewEditedFiles(null)
            if (fileUiModel.fileList.isEmpty()) {
                showEmptyView()
            } else {
                hideEmptyView()
            }
            mToolbar?.let {
                refreshScanModeItemIcon(it)
                updateEditAndSortMenu(it)
                setToolbarEditIcon(it, isChildDisplay)
            }
            setNormalHomeDisplay(viewModule.mPositionModel.value?.mCurrentPath)
            if (fileUiModel.fileList is ArrayList<BaseFileBean>) {
                mAdapter?.let {
                    it.setKeyWord(fileUiModel.keyWord)
                    mFolderTransformAnimator.mIsFolderInAnimation = viewModule.mIsFolderIn
                    it.setData(
                        fileUiModel.fileList as ArrayList<BaseFileBean>,
                        fileUiModel.selectedList,
                        viewModule.mNeedScroll
                    )
                }

            }
        }
    }

    private fun onPositionModelChanged(
        viewModule: SelectFileBrowserViewModel,
        positionModel: SelectFileBrowserViewModel.PositionModel
    ) {
        if (!viewModule.mModeState.initState) {
            return
        }
        HighlightUtil.cancelAnimation()
        mPathBar?.let {
            if (it.getCurrentPath() != positionModel.mCurrentPath) {
                it.setCurrentPath(positionModel.mCurrentPath)
            }
        }
        appBarLayout?.postDelayed({
            mLayoutManager?.scrollToPositionWithOffset(
                positionModel.mPosition,
                positionModel.mOffset
            )
            viewModule.mPositionModel.value?.mPosition = 0
            viewModule.mPositionModel.value?.mOffset = 0
            viewModule.mNeedScroll = false
        }, FILE_BROWSER_FOLDER_ANIM_DELAY)
    }

    private fun startCreateFolderObserve() {
        val viewModule = fragmentViewModel ?: return
        viewModule.createFolderPosition.observe(this) {
            if (!viewModule.mModeState.initState || it == -1) {
                return@observe
            }
            Log.d(TAG, "startCreateFolderObserve position:$it")
            val offset = KtViewUtils.getRecyclerViewTopOffset()
            mLayoutManager?.scrollToPositionWithOffset(it, -offset)
            lifecycleScope.launch(Dispatchers.Default) {
                delay(NOTIFY_CHANGED_DELAY)
                withContext(Dispatchers.Main) {
                    mAdapter?.apply {
                        HighlightUtil.highlightPosition = it
                        notifyItemChanged(it, 0)
                        viewModule.createFolderPosition.value = -1
                    }
                }
            }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            toolbar?.let {
                refreshScanModeItemIcon(it)
                setToolbarEditIcon(it, isChildDisplay)
            }
        }
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        HighlightUtil.cancelAnimation()
        mPathBar?.setOnPathClickListener(null)
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
    }

    private fun startScanModeObserver() {
        fragmentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            mToolbar?.let {
                val needSkipAnimation = mNeedSkipAnimation
                if (needSkipAnimation) {
                    refreshScanModeAdapter(scanMode)
                } else {
                    fragmentRecyclerView?.let { recyclerView ->
                        recyclerView.mTouchable = false
                        recyclerView.stopScroll()
                    }
                    mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                        override fun onSpanChangeCallback() {
                            mLayoutManager?.scrollToPosition(0)
                            refreshScanModeAdapter(scanMode)
                        }
                    }, object : OnAnimatorEndListener {
                        override fun onAnimatorEnd() {
                            fragmentRecyclerView?.mTouchable = true
                        }
                    })
                }
                delay { refreshScanModeItemIcon(it, needSkipAnimation) }
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            if (scanMode == KtConstants.SCAN_MODE_GRID) {
                refreshScanModeAdapter(scanMode)
            }
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
            }
            mSortPopupController.hideSortPopUp()
            mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
            if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
            updateFloatButtonMargin()
            updateLeftRightMargin()
        }
    }

    private fun updateFloatButtonMargin() {
        val activity = baseVMActivity ?: return
        createFolderFab?.updateLayoutParams<MarginLayoutParams> {
            marginEnd = if (UIConfigMonitor.getWindowType() == WindowType.LARGE) {
                activity.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_40dp)
            } else {
                activity.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_24dp)
            }
        }
    }

    override fun updateLeftRightMargin() {
        val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
        sortEntryView?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val fileList = fragmentViewModel?.uiState?.value?.fileList ?: return null
        val size = fileList.size
        selectedFiles?.forEach { fileBean ->
            val indexOf = fileList.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    getRecyclerView()?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = fragmentViewModel?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode,
            ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER)
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            mFolderTransformAnimator.mSkipAddRemoveAnimation = true
            notifyDataSetChanged()
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            val bgColor = context?.resources?.getColor(com.support.appcompat.R.color.coui_color_background_with_card)
            mLoadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    fragmentViewModel?.mFileBrowserCallBack?.isLoadNewPath()?.not() ?: false
                }
                bgColor?.let {
                    setBackgroundColor(it)
                }
                setDeleyShowTime(LOADING_DELAY_TIME)
                setShowAinimate(true)
                setDissapearAnimate(true)
                setShowLoadingTips(false)
                //这里蒋LoadingController和FolderTransformAnimator通过接口方式关联起来
                mFolderTransformAnimator.registerNeedSkipAnimator(this)
            }
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val desc: String
            val resId: Int = if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = appContext.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = appContext.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            setScanModeStatue(it, desc, needSkipAnimation, resId)
        }
    }

    private fun setScanModeStatue(
        toolbar: MenuItem,
        desc: String,
        needSkipAnimation: Boolean,
        resId: Int
    ) {
        if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE
            && fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true && isChildDisplay
        ) {
            toolbar.setIcon(null)
            toolbar.setTitle(desc)
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        } else {
            toolbar.setTitle(null)
            if (needSkipAnimation) {
                toolbar.setIcon(resId)
            } else {
                KtAnimationUtil.updateMenuItemWithFadeAnimate(toolbar, resId, baseVMActivity)
            }
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
        }
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            if (baseVMActivity?.sideNavigationStatus?.value == LIST_NORMAL_MODE
                && fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true && isChildDisplay) {
                it.setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            } else {
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val checkedCount = fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
        val isSelectAll = (fragmentViewModel?.getRealFileSize() == fragmentViewModel?.uiState?.value?.selectedList?.size)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        var isEnable = fragmentViewModel?.uiState?.value?.selectedList?.isNotEmpty() ?: false
        isEnable = isEnable && !DragUtils.isDragging
        if (baseVMActivity is NavigationInterface) {
            (baseVMActivity as NavigationInterface).setNavigateItemAble(
                isEnable, hasDrmFile(fragmentViewModel?.getSelectItems()))
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
            showEmptyView()
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        if ((baseVMActivity != null) && (rootView != null)) {
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!, addFileClickEvent = object : AddFileClickEvent {
                override fun onClick() {
                    performClickMenuEvent(R.id.navigation_new_folder)
                }
            })
            hasShowEmpty = true
            listEmptyFile()
        }
        mFileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
        mFileEmptyController.setEmptyGuidContent(View.VISIBLE, com.filemanager.common.R.string.menu_file_list_new_folder)
        sortEntryView?.visibility = View.INVISIBLE
        fragmentRecyclerView?.visibility = View.INVISIBLE
        createFolderFab?.visibility = View.GONE
        Log.d(TAG, "showEmptyView")
    }

    private fun hideEmptyView() {
        mFileEmptyController.hideFileEmptyView()
        sortEntryView?.visibility = View.VISIBLE
        fragmentRecyclerView?.visibility = View.VISIBLE
        createFolderFab?.visibility = View.VISIBLE
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        if (checkShowPermissionEmpty(false)) {
            sortEntryView?.setFileCount(0)
            return
        }
        mCurrentPath?.let {
            fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(this), it)
        }
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                baseVMActivity?.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay || isFromOTGList)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    override fun pressBack(): Boolean {
        val activity = activity ?: return false
        val result = fragmentViewModel?.pressBack() ?: false
        if (activity is OtgFileBrowserActivity) {
            return if (isFromOTGList) {
                activity.backToMoreStorage()
                true
            } else {
                result
            }
        }
        if (result) {
            previewEditedFiles(null)
        }
        if (!result) {
            val mainAction = Injector.injectFactory<IMain>()
            if (isFromDetail) {
                mainAction?.backPreviousFragment(-1, activity)
                return true
            } else if (isFromOTGList) {
                mainAction?.backPreviousFragment(CategoryHelper.CATEGORY_MORE_STORAGE, activity)
                return true
            }
        }
        return result
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.file_browser_menu, menu)
        mToolbar?.apply {
            mNeedSkipAnimation = true
            refreshScanModeItemIcon(this)
            setToolbarEditIcon(this, isChildDisplay)
            setToolbarMenuVisible(this, !isChildDisplay)
        }
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.onNavigationItemSelected(it, item)
        } ?: false
    }

    @Suppress("LongMethod")
    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(
                    baseVMActivity,
                    CategoryHelper.CATEGORY_FILE_BROWSER,
                    null,
                    fragmentViewModel?.mPositionModel?.value?.mCurrentPath
                )
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.ALL_STORAGE)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_OTG)
                true
            }
            R.id.actionbar_edit -> {
                if (actionCheckPermission().not()) {
                    baseVMActivity?.showSettingGuildDialog()
                    return true
                }
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected actionbar_edit mFileLoadState = STATE_START")
                } else {
                    StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_EDIT)
                    fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                    OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.ALL_STORAGE)
                }
                true
            }
            R.id.navigation_sort -> {
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START
                    && (mAdapter?.itemCount ?: 0) == 0) {
                    Log.d(TAG, "onMenuItemSelected navigation_sort mFileLoadState = STATE_START")
                } else {
                    baseVMActivity?.let {
                        StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                        OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.ALL_STORAGE)
                        val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                        mSortPopupController.showSortPopUp(
                            it,
                            -1,
                            anchorView,
                            SortRecordModeFactory.getBrowserKey(mCurrentPath),
                            object : SelectItemListener {

                                override fun onDismiss() {
                                    sortEntryView?.rotateArrow()
                                }

                                override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                    if (flag) {
                                        sortEntryView?.setSortOrder(sortMode, isDesc)
                                        fragmentViewModel?.sortReload()
                                    }
                                }
                            })
                    }
                }
                true
            }
            R.id.navigation_new_folder -> {
                if (actionCheckPermission().not()) {
                    baseVMActivity?.showSettingGuildDialog()
                    return true
                }
                fragmentViewModel?.mPositionModel?.value?.mCurrentPath?.also {
                    StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_NEW_FOLDER)
                    OptimizeStatisticsUtil.createFolder(OptimizeStatisticsUtil.CREATE_FOLDER_FROM_OTG)
                    activity?.let { ac -> mFileOperateController?.onCreateFolder(ac, it) }
                }
                true
            }
            R.id.actionbar_scan_mode -> {
                fragmentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            R.id.action_setting -> {
                StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_SETTING)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.ALL_STORAGE)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_OTG)
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                true
            }
            R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            else -> {
                false
            }
        }
    }

    override fun fromSelectPathResult(requestCode: Int, path: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, path) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
            fragmentViewModel?.loadData()
        }
    }

    override fun setCurrentFromOtherSide(currentPath: String) {
        mCurrentPath = currentPath
        fragmentViewModel?.mPathHelp?.updateRootPath(currentPath)
        fragmentViewModel?.setCurrentFromOtherSide(currentPath)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        fragmentViewModel?.isFromDetail = isFromDetail
        fragmentViewModel?.mCurrentPath = mCurrentPath ?: ""
        if (mIsSetInitLoad) {
            mIsSetInitLoad = false
            onResumeLoadData()
        }
    }

    override fun onItemClick(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            if (baseFile.mIsDirectory) {
                fragmentRecyclerView?.paddingTop?.let { paddingTop ->
                    val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
                    val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: paddingTop
                    fragmentViewModel?.onDirClick(baseVMActivity, baseFile, viewPosition, offset - paddingTop)
                }
            } else {
                activity?.let {
                    val previewResult = previewClickedFile(baseFile, fragmentViewModel?.previewClickedFileLiveData)
                    if (!previewResult) {
                        var mediaImgIds: ArrayList<String>? = null
                        // 判断当前点击是否是媒体库中的图片
                        lifecycleScope.launch(context = Dispatchers.IO) {
                            if (FileMediaHelper.isImgAndInMedia(baseFile)) {
                                // 获取当前视图中的图片文件，按照排序顺序生成一个media id列表，传给相册
                                mediaImgIds = FileMediaHelper.getMediaImgIds(
                                    baseFile,
                                    fragmentViewModel?.uiState?.value?.fileList
                                )
                                withContext(Dispatchers.Main) {
                                    mFileOperateController?.onFileClick(it, baseFile, e, mediaImgIds)
                                }
                            } else {
                                withContext(Dispatchers.Main) {
                                    mFileOperateController?.onFileClick(it, baseFile, e, mediaImgIds)
                                }
                            }
                        }
                    }
                }
            }
        }
        return true
    }

    private fun setNormalHomeDisplay(currentPath: String?) {
        baseVMActivity?.supportActionBar?.apply {
            currentPath?.let {
                if (fragmentViewModel?.mPathHelp?.isRootOtgPath(it) == true) {
                    setDisplayHomeAsUpEnabled(!isChildDisplay || isFromOTGList)
                } else {
                    setDisplayHomeAsUpEnabled(true)
                }
                setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        mToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
            refreshScanModeItemIcon(it)
            setToolbarEditIcon(it, isChildDisplay)
        }
        baseVMActivity?.supportActionBar?.apply {
            if (fragmentViewModel?.isInSelectMode() == true) {
                setDisplayHomeAsUpEnabled(true)
                setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
            } else {
                setNormalHomeDisplay(fragmentViewModel?.mPositionModel?.value?.mCurrentPath)
            }
        }
    }

    override fun backToTop() {
        fragmentRecyclerView?.fastSmoothScrollToTop()
    }

    override fun updatedLabel() {}

    override fun permissionSuccess() {}

    override fun getCurrentPath(): String {
        return fragmentViewModel?.mPositionModel?.value?.mCurrentPath ?: ""
    }

    override fun getScanMode(): Int {
        return fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        fragmentViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    fun onRefreshData() {
        fragmentViewModel?.loadData()
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyMarginTop()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_OTG_BROWSER
    }

    private fun previewClickedFile(file: BaseFileBean?, clickFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    private fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    private fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    private fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        if (isSelectionMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val sideNavigationWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            sideNavigationWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun refreshDataForDir(path: String, category: Int) {
        if (path == getCurrentPath() && category == CategoryHelper.CATEGORY_OTG_BROWSER) {
            onResumeLoadData()
        }
    }

    override fun renameToShortCutFolder(newName: String, file: BaseFileBean): String {
        return ""
    }

    override fun renameToLabel(newName: String, labelId: Long) {}

    override fun onClickDir(path: String) {
        val baseFile = BaseFileBean()
        baseFile.mData = path
        if (File(path).isDirectory) {
            baseFile.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        }
        if (baseFile.mIsDirectory) {
            fragmentRecyclerView?.paddingTop?.let { paddingTop ->
                val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
                val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: paddingTop
                fragmentViewModel?.onDirClick(baseVMActivity, baseFile, viewPosition, offset - paddingTop)
            }
        }
    }

    override fun getSelectItems(): ArrayList<out BaseFileBean>? {
        return fragmentViewModel?.getSelectItems()
    }
}