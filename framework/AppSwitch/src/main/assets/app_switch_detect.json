{"isOn": 1, "version": 3, "detect_fuffix": [".docx", ".doc", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".pdf", ".xml"], "rules": [{"package": "com.ss.android.lark", "range": {"minVersion": 7150750, "deviceType": 3}, "previewActivitys": [{"preview": "com.bytedance.ee.bear.drive.core.DriveActivity", "checkActivity": [{"ac": "com.ss.android.lark.chatwindow.ChatWindowActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n\\|发送给 ([\\S| ]*)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "sourcename", "top_level_reg": "\\n\\|可以向自己发送文件或转发消息\\n*[\\s|\\S]*\\n([\\S| ]+)\\n消息\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "<PERSON><PERSON>lena<PERSON>", "top_level_reg": "\\n*(\\S+{suffix})\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*(\\S+ \\S+)\\n{filename}\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*) (MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*) (KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*) (GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+) (Byte)", "extract_reg_index_type": 0, "int_index": 0, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "\\n(\\d+) 名群成员\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0}]}, {"preview": "com.bytedance.ee.bear.document.DocActivity", "checkActivity": [{"ac": "com.ss.android.lark.main.app.MainActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*(云文档)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 2}, {"target_type": "<PERSON><PERSON>lena<PERSON>", "top_level_reg": "\\n*(\\S+{suffix})\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "time", "top_level_reg": "\\n{filename}\\n(最近修改于|最近访问于) +(\\S+ *\\S*)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{2,4})年(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2, "hour_index": 3, "minute_index": 4}, {"extract_reg": "(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "month_index": 0, "day_index": 1, "hour_index": 2, "minute_index": 3}, {"extract_reg": "昨天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -1, "hour_index": 0, "minute_index": 1}, {"extract_reg": "前天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -2, "hour_index": 0, "minute_index": 1}, {"extract_reg": "(\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": 0, "hour_index": 0, "minute_index": 1}]}], "forceUpdate": 0, "sourceType": 2, "sourceName": "云文档"}, {"ac": "com.bytedance.ee.bear.lark.route.SpaceRouteActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n(我的空间|共享空间|收藏|离线)\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 2}, {"target_type": "<PERSON><PERSON>lena<PERSON>", "top_level_reg": "\\n*(\\S+{suffix})\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "time", "top_level_reg": "\\n{filename}\\n\\S*\\s*\\S*\\s*(最近修改于|最近访问于|共享于|收藏于) +(\\S+ *\\S*)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{2,4})年(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2, "hour_index": 3, "minute_index": 4}, {"extract_reg": "(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "month_index": 0, "day_index": 1, "hour_index": 2, "minute_index": 3}, {"extract_reg": "昨天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -1, "hour_index": 0, "minute_index": 1}, {"extract_reg": "前天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -2, "hour_index": 0, "minute_index": 1}, {"extract_reg": "(\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": 0, "hour_index": 0, "minute_index": 1}]}, {"target_type": "size", "top_level_reg": "\\n{filename}\\n(\\S*\\s*\\S*)\\s*(最近修改于|最近访问于|共享于|收藏于) +(\\S+ *\\S*)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*) (MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*) (KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*) (GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+) (Byte)", "extract_reg_index_type": 0, "int_index": 0, "unit": 1}]}], "forceUpdate": 0, "sourceType": 2, "sourceName": "云文档"}]}, {"preview": "com.bytedance.ee.bear.drive.core.DriveExpandActivity", "checkActivity": [{"ac": "com.ss.android.lark.chatwindow.ChatWindowActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n\\|发送给 ([\\S| ]*)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "sourcename", "top_level_reg": "\\n\\|可以向自己发送文件或转发消息\\n*[\\s|\\S]*\\n([\\S| ]+)\\n消息\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "<PERSON><PERSON>lena<PERSON>", "top_level_reg": "\\n*(\\S+{suffix})\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*(\\S+ \\S+)\\n{filename}\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*) (MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*) (KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*) (GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+) (Byte)", "extract_reg_index_type": 0, "int_index": 0, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "\\n(\\d+) 名群成员\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0}]}, {"preview": "com.bytedance.ee.bear.document.DocExpandActivity", "checkActivity": [{"ac": "com.ss.android.lark.main.app.MainActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*(云文档)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 2}, {"target_type": "<PERSON><PERSON>lena<PERSON>", "top_level_reg": "\\n*(\\S+{suffix})\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "time", "top_level_reg": "\\n{filename}\\n(最近修改于|最近访问于) +(\\S+ *\\S*)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{2,4})年(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2, "hour_index": 3, "minute_index": 4}, {"extract_reg": "(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "month_index": 0, "day_index": 1, "hour_index": 2, "minute_index": 3}, {"extract_reg": "昨天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -1, "hour_index": 0, "minute_index": 1}, {"extract_reg": "前天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -2, "hour_index": 0, "minute_index": 1}, {"extract_reg": "(\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": 0, "hour_index": 0, "minute_index": 1}]}], "forceUpdate": 0, "sourceType": 2, "sourceName": "云文档"}, {"ac": "com.bytedance.ee.bear.lark.route.SpaceRouteActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n(我的空间|共享空间|收藏|离线)\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 2}, {"target_type": "<PERSON><PERSON>lena<PERSON>", "top_level_reg": "\\n*(\\S+{suffix})\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "time", "top_level_reg": "\\n{filename}\\n\\S*\\s*\\S*\\s*(最近修改于|最近访问于|共享于|收藏于) +(\\S+ *\\S*)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{2,4})年(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2, "hour_index": 3, "minute_index": 4}, {"extract_reg": "(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "month_index": 0, "day_index": 1, "hour_index": 2, "minute_index": 3}, {"extract_reg": "昨天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -1, "hour_index": 0, "minute_index": 1}, {"extract_reg": "前天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -2, "hour_index": 0, "minute_index": 1}, {"extract_reg": "(\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": 0, "hour_index": 0, "minute_index": 1}]}, {"target_type": "size", "top_level_reg": "\\n{filename}\\n(\\S*\\s*\\S*)\\s*(最近修改于|最近访问于|共享于|收藏于) +(\\S+ *\\S*)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*) (MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*) (KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*) (GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+) (Byte)", "extract_reg_index_type": 0, "int_index": 0, "unit": 1}]}], "forceUpdate": 0, "sourceType": 2, "sourceName": "云文档"}]}]}, {"package": "com.tencent.mm", "range": {"minVersion": 2560, "deviceType": 3}, "previewActivitys": [{"preview": "com.tencent.mm.pluginsdk.ui.tools.MiniQBReaderUI", "checkActivity": [{"ac": "com.tencent.mm.ui.LauncherUI", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*([\\S| ]+)\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*((\\d+)\\.?(\\d*)(MB|KB|K|GB|B))\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "top_level_reg_match_mode": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*) (MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*) (KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*) (GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*) (B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "\\n*(\\S+\\(\\d+\\))\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0}, {"ac": "com.tencent.mm.ui.chatting.ChattingUI", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*([\\S| ]+)\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*((\\d+)\\.?(\\d*)(MB|KB|K|GB))\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "top_level_reg_match_mode": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*) (MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*) (KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*) (GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*) (B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "\\n*(\\S+\\(\\d+\\))\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0}, {"ac": "com.tencent.mm.plugin.record.ui.RecordMsgDetailUI", "parse_reg": [{"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}]}, {"target_type": "basetime", "top_level_reg": "^((\\d{2,4})年(\\d{1,2})月(\\d{1,2})日)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d{2,4})年(\\d{1,2})月(\\d{1,2})日", "year_index": 0, "month_index": 1, "day_index": 2}]}, {"target_type": "deltatime", "top_level_reg": "\\n(\\S+)\\n{filename}\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1, "second_index": 2}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}, {"ac": "com.tencent.mm.ui.chatting.gallery.MediaHistoryListUI", "parse_reg": [{"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+ \\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*) (MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*) (KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*) (GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}]}, {"target_type": "time", "top_level_reg": "\\n(\\S+)\\n{filename}\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d{2,4})年(\\d{1,2})月(\\d{1,2})日", "year_index": 0, "month_index": 1, "day_index": 2}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "今天", "day_adjust": 0}, {"extract_reg": "(\\d+)天前", "day_adjust_index": 0}]}], "forceUpdate": 0, "sourceType": 4, "sourceName": "聊天记录"}, {"ac": "com.tencent.mm.plugin.fav.ui.FavoriteIndexUI", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*([\\S| ]+)\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{rawfilename}\\n\\S+ (\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}]}, {"target_type": "time", "top_level_reg": "\\n{rawfilename}\\n[\\S| ]*\\n[\\S| ]*\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d{2,4})年(\\d{1,2})月(\\d{1,2})日", "year_index": 0, "month_index": 1, "day_index": 2}, {"extract_reg": "(\\d{1,2})月(\\d{1,2})日", "month_index": 0, "day_index": 1}]}], "forceUpdate": 0, "sourceType": 7}]}]}, {"package": "com.tencent.wework", "range": {"minVersion": 28160, "deviceType": 3}, "previewActivitys": [{"preview": "com.tencent.wework.common.controller.TbsFileActivity", "checkActivity": [{"ac": "com.tencent.wework.msg.controller.MessageListActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "^(\\S+)\\(\\d+\\)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0, "ignoreSpecialCharForViewExact": "[\\f\\n\\r\\t\\v]", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.wework.msg.controller.ExternalContactMessageListActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "^(\\S+)\\(\\d+\\)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0, "ignoreSpecialCharForViewExact": "[\\f\\n\\r\\t\\v]", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.wework.msg.controller.ExternalGroupMessageListActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "defaultSourceType": 1, "ignoreSpecialCharForViewExact": "[\\f\\n\\r\\t\\v]", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.wework.msg.controller.ExternalWechatUserMessageListActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "^(\\S+)\\(\\d+\\)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0, "ignoreSpecialCharForViewExact": "[\\f\\n\\r\\t\\v]", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.pb.collectionfile.controller.ChatRecordDetailActivity", "parse_reg": [{"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "top_level_reg_match_mode": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n*(\\S+\\u00a0\\u00a0\\S+)\\n{filename}\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d{2,4})/(\\d{1,2})/(\\d{1,2})/\\u00a0\\u00a0(\\d{1,2}):(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2, "hour_index": 3, "minute_index": 4}, {"extract_reg": "(\\d{1,2})/(\\d{1,2})\\u00a0\\u00a0(\\d{1,2}):(\\d{1,2})", "month_index": 0, "day_index": 1, "hour_index": 2, "minute_index": 3}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.wework.msg.controller.MessageListWechatFileDownloadPreviewActivity", "parse_reg": [{"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "top_level_reg_match_mode": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录", "ignoreVisibleToUserForViewExact": true}]}, {"preview": "com.tencent.wework.msg.controller.ShowHighLightCodeActivity", "checkActivity": [{"ac": "com.tencent.wework.msg.controller.MessageListActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "^(\\S+)\\(\\d+\\)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0, "ignoreSpecialCharForViewExact": "[\\f\\n\\r\\t\\v]", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.wework.msg.controller.ExternalContactMessageListActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "^(\\S+)\\(\\d+\\)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0, "ignoreSpecialCharForViewExact": "[\\f\\n\\r\\t\\v]", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.wework.msg.controller.ExternalWechatUserMessageListActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "^(\\S+)\\(\\d+\\)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0, "ignoreSpecialCharForViewExact": "[\\f\\n\\r\\t\\v]", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.wework.msg.controller.ExternalGroupMessageListActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "([\\S| ]+)\\(\\d+\\)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}, {"extract_reg": "([\\S| ]+)", "extract_reg_index_type": 0, "extract_reg_index_value": 0}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "defaultSourceType": 1, "ignoreSpecialCharForViewExact": "[\\f\\n\\r\\t\\v]", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.pb.collectionfile.controller.ChatRecordDetailActivity", "parse_reg": [{"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "top_level_reg_match_mode": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n*(\\S+\\u00a0\\u00a0\\S+)\\n{filename}\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d{2,4})/(\\d{1,2})/(\\d{1,2})/\\u00a0\\u00a0(\\d{1,2}):(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2, "hour_index": 3, "minute_index": 4}, {"extract_reg": "(\\d{1,2})/(\\d{1,2})\\u00a0\\u00a0(\\d{1,2}):(\\d{1,2})", "month_index": 0, "day_index": 1, "hour_index": 2, "minute_index": 3}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录", "ignoreVisibleToUserForViewExact": true}, {"ac": "com.tencent.wework.msg.controller.MessageListWechatFileDownloadPreviewActivity", "parse_reg": [{"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "top_level_reg_match_mode": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录", "ignoreVisibleToUserForViewExact": true}]}]}, {"package": "com.tencent.mobileqq", "range": {"minVersion": 4766, "maxVersion": 5539, "deviceType": 3}, "previewActivitys": [{"preview": "com.tencent.mobileqq.filebrowser.FileBrowserActivity", "checkActivity": [{"ac": "com.tencent.mobileqq.activity.SplashActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n发送\\n*\\d*\\n([\\S| ]+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "\\n*{filename}\\n(\\S+)( / \\S+)*\\n群文件\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0}, {"ac": "com.dataline.activities.LiteActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "defaultSourceType": 0}, {"ac": "com.tencent.mobileqq.activity.MultiForwardActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*([\\S| ]+)的聊天记录\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}, {"ac": "com.tencent.mobileqq.activity.history.NTChatHistoryActivity", "parse_reg": [{"target_type": "size", "top_level_reg": "\\n*{filename}\\n( )*\\S+  来自 *\\S+  (\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+ *\\n){2,4}(\\d+\\.?\\d*(MB|M|KB|K|GB|G|B))", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n*{filename}\\n( )*(\\S+)  来自 *\\S+  \\S+\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "前天", "day_adjust": -2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})", "month_index": 0, "day_index": 1}]}, {"target_type": "time", "top_level_reg": "\\n*{filename}\\n(\\S+) *\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "前天", "day_adjust": -2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})", "month_index": 0, "day_index": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}]}, {"preview": "com.tencent.mobileqq.activity.QQTranslucentBrowserActivity", "checkActivity": [{"ac": "com.tencent.mobileqq.activity.MultiForwardActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*([\\S| ]+)的聊天记录\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}, {"ac": "cooperation.troop.TroopFileProxyActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^群文件\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 3, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "前天", "day_adjust": -2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})-(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})", "month_index": 0, "day_index": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}, {"ac": "com.tencent.mobileqq.activity.SplashActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n发送\\n*\\d*\\n([\\S| ]+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "\\n*{filename}\\n(\\S+)( / \\S+)*\\n群文件\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0}]}, {"preview": "com.tencent.mobileqq.filemanager.fileviewer.TroopFileDetailBrowserActivity", "checkActivity": [{"ac": "cooperation.troop.TroopFileProxyActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^群文件\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 3, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "前天", "day_adjust": -2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})-(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})", "month_index": 0, "day_index": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}]}, {"preview": "com.tencent.mobileqq.activity.QQBrowserActivity", "checkActivity": [{"ac": "cooperation.troop.TroopFileProxyActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^群文件\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 3, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "前天", "day_adjust": -2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})-(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})", "month_index": 0, "day_index": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}]}]}, {"package": "com.tencent.mobileqq", "range": {"minVersion": 5540, "deviceType": 3}, "previewActivitys": [{"preview": "com.tencent.mobileqq.filebrowser.FileBrowserActivity", "checkActivity": [{"ac": "com.tencent.mobileqq.activity.SplashActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n发送\\n([\\S| ]+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "\\n*{filename}\\n(\\S+)( / \\S+)*\\n群文件\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0}, {"ac": "com.dataline.activities.LiteActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "defaultSourceType": 0}, {"ac": "com.tencent.mobileqq.activity.MultiForwardActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*([\\S| ]+)的聊天记录\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}, {"ac": "com.tencent.mobileqq.activity.history.NTChatHistoryActivity", "parse_reg": [{"target_type": "size", "top_level_reg": "\\n*{filename}\\n( )*\\S+  来自 *\\S+  (\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+ *\\n){2,4}(\\d+\\.?\\d*(MB|M|KB|K|GB|G|B))", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n*{filename}\\n( )*(\\S+)  来自 *\\S+  \\S+\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "前天", "day_adjust": -2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})", "month_index": 0, "day_index": 1}]}, {"target_type": "time", "top_level_reg": "\\n*{filename}\\n(\\S+) *\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "前天", "day_adjust": -2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})", "month_index": 0, "day_index": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}]}, {"preview": "com.tencent.mobileqq.activity.QQTranslucentBrowserActivity", "checkActivity": [{"ac": "com.tencent.mobileqq.activity.MultiForwardActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*([\\S| ]+)的聊天记录\\n$", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}, {"ac": "cooperation.troop.TroopFileProxyActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^群文件\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 3, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "前天", "day_adjust": -2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})-(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})", "month_index": 0, "day_index": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}, {"ac": "com.tencent.mobileqq.activity.SplashActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n发送\\n([\\S| ]+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+) / (\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "sourcetype", "top_level_reg": "\\n*{filename}\\n(\\S+)( / \\S+)*\\n群文件\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "reg_match_source_type": 1, "reg_nomatch_source_type": 0}], "forceUpdate": 0, "defaultSourceType": 0}]}, {"preview": "com.tencent.mobileqq.filemanager.fileviewer.TroopFileDetailBrowserActivity", "checkActivity": [{"ac": "cooperation.troop.TroopFileProxyActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "^群文件\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 3, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB|M)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB|G)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n*{filename}\\n( )*(\\S+) *(来自 )*\\S* *(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 1, "second_level_regs": [{"extract_reg": "(\\d{1,2}):(\\d{1,2})", "hour_index": 0, "minute_index": 1}, {"extract_reg": "昨天", "day_adjust": -1}, {"extract_reg": "前天", "day_adjust": -2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})-(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2}, {"extract_reg": "(\\d{1,2})-(\\d{1,2})", "month_index": 0, "day_index": 1}]}], "forceUpdate": 0, "sourceType": 3, "sourceName": "聊天记录"}]}]}, {"package": "com.alibaba.android.rimet", "range": {"minVersion": 1106, "deviceType": 3}, "previewActivitys": [{"preview": "com.alibaba.dingtalk.cspace.activity.SpaceNewPreviewActivity", "checkActivity": [{"ac": "com.alibaba.android.dingtalkim.activities.ChatMsgActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*\\ueb1c\\n*[\\ue83d]*\\n*\\d*\\n([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "sourcename", "top_level_reg": "\\n*\\ueb1c\\n\\|记录一下\\n*\\d*\\n([\\S| ]+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "top_level_reg_match_mode": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}], "forceUpdate": 0, "defaultSourceType": 0}, {"ac": "com.alibaba.android.dingtalkim.activities.ChatListDetailActivity", "parse_reg": [{"target_type": "sourcename", "top_level_reg": "\\n*([\\S| ]+)\\n\\ue950\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0}, {"target_type": "size", "top_level_reg": "\\n*{filename}\\n(\\S+)\\n", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "top_level_reg_match_mode": 1, "second_level_regs": [{"extract_reg": "(\\d+)\\.?(\\d*)(MB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1048576}, {"extract_reg": "(\\d+)\\.?(\\d*)(KB|K)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1024}, {"extract_reg": "(\\d+)\\.?(\\d*)(GB)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1073741824}, {"extract_reg": "(\\d+)\\.?(\\d*)(B)", "extract_reg_index_type": 0, "int_index": 0, "decimal_index": 1, "unit": 1}]}, {"target_type": "time", "top_level_reg": "\\n(\\S+ *\\S*)\\n{filename}\\n*", "top_level_reg_index_type": 0, "top_level_reg_index_value": 0, "second_level_regs": [{"extract_reg": "(\\d{2,4})年(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "year_index": 0, "month_index": 1, "day_index": 2, "hour_index": 3, "minute_index": 4}, {"extract_reg": "(\\d{1,2})月(\\d{1,2})日 (\\d{1,2}):(\\d{1,2})", "month_index": 0, "day_index": 1, "hour_index": 2, "minute_index": 3}, {"extract_reg": "昨天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -1, "hour_index": 0, "minute_index": 1}, {"extract_reg": "前天 (\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": -2, "hour_index": 0, "minute_index": 1}, {"extract_reg": "(\\d{1,2}):(\\d{1,2})", "extract_reg_index_type": 0, "day_adjust": 0, "hour_index": 0, "minute_index": 1}]}], "forceUpdate": 0, "sourceType": 3, "noParseFromIntent": "sourcetype", "sourceName": "聊天记录"}]}]}]}