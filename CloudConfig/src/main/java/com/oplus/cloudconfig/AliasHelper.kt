/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AliasHelper.kt
 * * Description : alias and alias cache
 * * Version     : 1.0
 * * Date        : 2020/9/7
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.cloudconfig

import com.filemanager.common.MyApplication
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.oplus.cloudconfig.bean.AliasBean
import java.io.File
import java.util.*
import java.util.concurrent.ConcurrentHashMap

object AliasHelper {
    private const val TAG = "AliasHelper"
    const val IS_DEFAULT_ALIAS = "1"
    const val STRING_FOUR_SPACES = "    "

    val mAliasMap: ConcurrentHashMap<String, AliasBean> by lazy {
        ConcurrentHashMap<String, AliasBean>()
    }

    val mInternalSdPath: String by lazy {
        VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
    }

    val mRtlState: Boolean by lazy {
        MyApplication.sAppContext.resources.getBoolean(com.filemanager.common.R.bool.file_note_rtl)
    }

    fun addAlias(aliasBean: List<AliasBean>) {
        val installedAPP: HashMap<String, Boolean> = KtAppUtils.getInstalledMap()
        aliasBean.filter {
            needAddAlias(it, installedAPP)
        }.forEach {
            Log.d(TAG, "add alias path:${it.mPath}  alias:${it.mAlias}")
            val key = mInternalSdPath.plus(File.separator).plus(it.mPath.toLowerCase(Locale.getDefault()))
            mAliasMap[key] = it
        }
    }

    /**
     * Return [AliasBean.mDefault] is "1" and [AliasBean.mPackageName] is installed APP return true
     */
    private fun needAddAlias(aliasBean: AliasBean, installedAPP: HashMap<String, Boolean>): Boolean {
        return aliasBean.takeIf {
            aliasBean.isNotEmpty()
        }?.let {
            (it.mDefault == IS_DEFAULT_ALIAS) || (installedAPP[it.mPackageName] == true)
        } ?: false
    }

    fun isEmpty() = mAliasMap.isEmpty()
}