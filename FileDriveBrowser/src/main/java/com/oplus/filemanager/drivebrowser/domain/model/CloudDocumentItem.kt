/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudDocumentItem
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/7 19:41
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.domain.model

import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsDocumentItem
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem
import com.oplus.filemanager.room.model.DriveFileEntity
import org.apache.commons.io.FilenameUtils

data class CloudDocumentItem(
    var folderId: String = "",
    var id: String = "",
    var title: String = "",
    var type: String = "",
    var url: String = "",
    var createTime: Int = 0,
    var lastModifyTime: Int = 0
) {

    /**
     * 文件大小，腾讯文档没有该值
     */
    var size: Int = -1

    companion object {
        fun map(item: CloudDocumentItem, source: String): DriveFileEntity {
            val entity = DriveFileEntity(0L)
            entity.name = item.title
            if (item.type == DocumentListItem.TENCENT_FILE_TYPE_FOLDER || item.type == KDocsDocumentItem.KDOCS_TYPE_SHARE_FOLDER) {
                entity.type = DriveFileEntity.TYPE_FOLDER
                entity.mimeType = item.type
            } else {
                entity.type = DriveFileEntity.TYPE_FILE
                entity.mimeType = item.type
                if (source == DriveFileEntity.SOURCE_TYPE_KINGSSOFT) { // 金山文档文件名有后缀
                    entity.mimeType = FilenameUtils.getExtension(item.title)
                }
            }
            entity.uri = item.url
            entity.createTime = item.createTime
            entity.lastModifyTime = item.lastModifyTime
            entity.fileId = item.id
            entity.size = item.size
            entity.parentId = item.folderId
            entity.source = source
            return entity
        }
    }
}