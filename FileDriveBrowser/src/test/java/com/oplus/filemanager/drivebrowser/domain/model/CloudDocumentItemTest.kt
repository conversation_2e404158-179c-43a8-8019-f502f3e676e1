package com.oplus.filemanager.drivebrowser.domain.model

import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsDocumentItem
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem
import com.oplus.filemanager.room.model.DriveFileEntity
import org.apache.commons.io.FilenameUtils
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * CloudDocumentItem 类的单元测试
 * 主要测试 CloudDocumentItem.map() 方法将云文档项转换为本地数据库实体 DriveFileEntity 的功能
 */
class CloudDocumentItemTest {

    /**
     * 在每个测试方法执行前调用
     * 设置时区为UTC，确保测试环境一致
     */
    @Before
    fun setUp() {
        // 确保每次测试前重置所有可能的状态
        System.setProperty("user.timezone", "UTC")
    }

    /**
     * 测试腾讯文档文件夹类型的映射
     * 验证文件夹类型能正确映射为 DriveFileEntity 的文件夹类型
     */
    @Test
    fun `test map with folder type from tencent`() {
        // Given - 准备测试数据：腾讯文档的文件夹项
        val item = CloudDocumentItem(
            folderId = "parent1",
            id = "folder1",
            title = "Test Folder",
            type = DocumentListItem.TENCENT_FILE_TYPE_FOLDER,
            url = "http://test.com/folder1",
            createTime = 123456,
            lastModifyTime = 123457
        ).apply {
            size = 1024
        }

        // When - 执行映射操作
        val result = CloudDocumentItem.map(item, "tencent")

        // Then - 验证映射结果
        assertEquals("Test Folder", result.name)
        assertEquals(DriveFileEntity.TYPE_FOLDER, result.type)
        assertEquals(DocumentListItem.TENCENT_FILE_TYPE_FOLDER, result.mimeType)
        assertEquals("http://test.com/folder1", result.uri)
        assertEquals(123456, result.createTime)
        assertEquals(123457, result.lastModifyTime)
        assertEquals("folder1", result.fileId)
        assertEquals(1024, result.size)
        assertEquals("parent1", result.parentId)
        assertEquals("tencent", result.source)
    }

    /**
     * 测试金山文档共享文件夹类型的映射
     * 验证金山文档的共享文件夹能正确映射
     */
    @Test
    fun `test map with folder type from kingssoft`() {
        // Given - 准备测试数据：金山文档的共享文件夹项
        val item = CloudDocumentItem(
            folderId = "parent2",
            id = "folder2",
            title = "Shared Folder",
            type = KDocsDocumentItem.KDOCS_TYPE_SHARE_FOLDER,
            url = "http://test.com/folder2",
            createTime = 123458,
            lastModifyTime = 123459
        ).apply {
            size = 2048
        }

        // When - 执行映射操作
        val result = CloudDocumentItem.map(item, DriveFileEntity.SOURCE_TYPE_KINGSSOFT)

        // Then - 验证映射结果
        assertEquals("Shared Folder", result.name)
        assertEquals(DriveFileEntity.TYPE_FOLDER, result.type)
        assertEquals(KDocsDocumentItem.KDOCS_TYPE_SHARE_FOLDER, result.mimeType)
        assertEquals("http://test.com/folder2", result.uri)
        assertEquals(123458, result.createTime)
        assertEquals(123459, result.lastModifyTime)
        assertEquals("folder2", result.fileId)
        assertEquals(2048, result.size)
        assertEquals("parent2", result.parentId)
        assertEquals(DriveFileEntity.SOURCE_TYPE_KINGSSOFT, result.source)
    }

    /**
     * 测试腾讯文档文件类型的映射
     * 验证普通文件类型能正确映射
     */
    @Test
    fun `test map with file type from tencent`() {
        // Given - 准备测试数据：腾讯文档的文件项
        val item = CloudDocumentItem(
            folderId = "parent3",
            id = "file1",
            title = "test.txt",
            type = "text",
            url = "http://test.com/file1",
            createTime = 123460,
            lastModifyTime = 123461
        ).apply {
            size = 512
        }

        // When - 执行映射操作
        val result = CloudDocumentItem.map(item, "tencent")

        // Then - 验证映射结果
        assertEquals("test.txt", result.name)
        assertEquals(DriveFileEntity.TYPE_FILE, result.type)
        assertEquals("text", result.mimeType)
        assertEquals("http://test.com/file1", result.uri)
        assertEquals(123460, result.createTime)
        assertEquals(123461, result.lastModifyTime)
        assertEquals("file1", result.fileId)
        assertEquals(512, result.size)
        assertEquals("parent3", result.parentId)
        assertEquals("tencent", result.source)
    }

    /**
     * 测试金山文档文件类型的映射
     * 验证金山文档的文件名后缀能正确作为mimeType
     */
    @Test
    fun `test map with file type from kingssoft should use file extension as mimeType`() {
        // Given - 准备测试数据：金山文档的文件项
        val item = CloudDocumentItem(
            folderId = "parent4",
            id = "file2",
            title = "document.pdf",
            type = "pdf",
            url = "http://test.com/file2",
            createTime = 123462,
            lastModifyTime = 123463
        ).apply {
            size = 1024
        }

        // When - 执行映射操作
        val result = CloudDocumentItem.map(item, DriveFileEntity.SOURCE_TYPE_KINGSSOFT)

        // Then - 验证映射结果
        assertEquals("document.pdf", result.name)
        assertEquals(DriveFileEntity.TYPE_FILE, result.type)
        assertEquals("pdf", result.mimeType)
        assertEquals("http://test.com/file2", result.uri)
        assertEquals(123462, result.createTime)
        assertEquals(123463, result.lastModifyTime)
        assertEquals("file2", result.fileId)
        assertEquals(1024, result.size)
        assertEquals("parent4", result.parentId)
        assertEquals(DriveFileEntity.SOURCE_TYPE_KINGSSOFT, result.source)
    }

    /**
     * 测试空值的映射
     * 验证当输入项所有字段为空时的默认映射行为
     */
    @Test
    fun `test map with empty values`() {
        // Given - 准备空值的测试数据
        val item = CloudDocumentItem()

        // When - 执行映射操作
        val result = CloudDocumentItem.map(item, "unknown")

        // Then - 验证默认值
        assertEquals("", result.name)
        assertEquals(DriveFileEntity.TYPE_FILE, result.type)
        assertEquals("", result.mimeType)
        assertEquals("", result.uri)
        assertEquals(0, result.createTime)
        assertEquals(0, result.lastModifyTime)
        assertEquals("", result.fileId)
        assertEquals(-1, result.size)
        assertEquals("", result.parentId)
        assertEquals("unknown", result.source)
    }

    /**
     * 测试金山文档无后缀文件名的映射
     * 验证当文件名没有后缀时mimeType应为空
     */
    @Test
    fun `test map with file name without extension from kingssoft`() {
        // Given - 准备无后缀文件名的测试数据
        val item = CloudDocumentItem(
            title = "document",
            type = "unknown"
        )

        // When - 执行映射操作
        val result = CloudDocumentItem.map(item, DriveFileEntity.SOURCE_TYPE_KINGSSOFT)

        // Then - 验证mimeType为空
        assertEquals("document", result.name)
        assertEquals(DriveFileEntity.TYPE_FILE, result.type)
        assertEquals("", result.mimeType)
    }

    /**
     * 测试特殊字符文件名的映射
     * 验证包含特殊字符的文件名能正确解析后缀
     */
    @Test
    fun `test map with special characters in file name`() {
        // Given - 准备包含特殊字符的文件名测试数据
        val item = CloudDocumentItem(
            title = "测试文件@#\$%.docx",
            type = "unknown"
        )

        // When - 执行映射操作
        val result = CloudDocumentItem.map(item, DriveFileEntity.SOURCE_TYPE_KINGSSOFT)

        // Then - 验证能正确解析后缀
        assertEquals("测试文件@#\$%.docx", result.name)
        assertEquals(DriveFileEntity.TYPE_FILE, result.type)
        assertEquals("docx", result.mimeType)
    }
}