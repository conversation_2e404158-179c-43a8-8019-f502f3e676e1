/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : CloudFileDownloadActionTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/2/26 14:44
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/2/26       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.download

import android.content.Context
import android.os.Looper
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.download.DocumentDownloadDispatcher
import com.oplus.filemanager.drivebrowser.ui.CloudDocsItem
import io.mockk.coEvery
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class CloudFileDownloadActionTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        context = mockk()
        every { context.applicationContext }.returns(context)
        mockkObject(MyApplication)
        every { appContext }.returns(context)
        mockkStatic(Looper::class)
        every { Looper.getMainLooper() } returns mockk(relaxed = true)
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
        unmockkStatic(Looper::class)
    }

    @Test
    fun testRun() {
        mockkStatic(PermissionUtils::class)
        every { PermissionUtils.hasNotificationPermission(any()) }.returns(true)
        val repository = mockk<FileDriveRepository>()
        coEvery { repository.isFileAuth(any()) } returns true
        mockkObject(DocumentDownloadDispatcher.Companion)
        justRun { DocumentDownloadDispatcher.createEnqueueDownloadIntent(any(), any(), any(), any(), any()) }
        val item = CloudDocsItem()
        val lifecycle = mockk<LifecycleOwner>()
        val files = ArrayList<CloudDocsItem>()
        val action = CloudFileDownloadAction(repository, lifecycle, CategoryHelper.CATEGORY_TENCENT_DOCS, files, TARGET_PATH)
        val result = action.run()
        Assert.assertFalse(result)

        files.add(item)
        val noLimitAction = CloudFileDownloadAction(repository, lifecycle, CategoryHelper.CATEGORY_TENCENT_DOCS, files, TARGET_PATH)
        val noLimitResult = noLimitAction.run()
        Assert.assertTrue(noLimitResult)

        files.add(CloudDocsItem())
        val moreThanOneAction =
            CloudFileDownloadAction(repository, lifecycle, CategoryHelper.CATEGORY_TENCENT_DOCS, files, TARGET_PATH)
        val moreThanOneResult = moreThanOneAction.run()
        Assert.assertFalse(moreThanOneResult)

        unmockkObject(DocumentDownloadDispatcher.Companion)
        unmockkStatic(PermissionUtils::class)
    }

    @Test
    fun testInternalDownload() {
        mockkStatic(PermissionUtils::class)
        every { PermissionUtils.hasNotificationPermission(any()) }.returns(true)

        mockkObject(DocumentDownloadDispatcher.Companion)
        justRun { DocumentDownloadDispatcher.createEnqueueDownloadIntent(any(), any(), any(), any(), any()) }
        val item = CloudDocsItem()
        val lifecycle = mockk<LifecycleOwner>()
        val files = ArrayList<CloudDocsItem>()
        val repository = mockk<FileDriveRepository>()
        coEvery { repository.isFileAuth(any()) } returns true
        val action = spyk(CloudFileDownloadAction(repository, lifecycle, CategoryHelper.CATEGORY_TENCENT_DOCS, files, TARGET_PATH))
        val result = action.internalDownload(item)
        Assert.assertTrue(result)
        verify { DocumentDownloadDispatcher.createEnqueueDownloadIntent(any(), any(), any(), any(), any()) }

        every { PermissionUtils.hasNotificationPermission(any()) }.returns(false)
        every { action.syncDownload(item) }.returns(true)
        action.internalDownload(item)
        verify { action.syncDownload(item) }
        unmockkObject(DocumentDownloadDispatcher.Companion)
        unmockkStatic(PermissionUtils::class)
    }

    @Test
    fun testSyncDownload() {
        mockkStatic("com.filemanager.common.utils.StringResourcesKt")
        every { stringResource(any()) }.returns(CLOUD_DOCS_ITEM_TITLE)
        val item = CloudDocsItem().apply {
            id = CLOUD_DOCS_ITEM_ID
            title = CLOUD_DOCS_ITEM_TITLE
            type = CLOUD_DOCS_ITEM_TYPE
            url = CLOUD_DOCS_ITEM_URL
        }
        val lifecycle = mockk<LifecycleOwner>()
        val files = ArrayList<CloudDocsItem>()
        val repository = mockk<FileDriveRepository>()
        coEvery { repository.isFileAuth(any()) } returns true
        val action = spyk(CloudFileDownloadAction(repository, lifecycle, CategoryHelper.CATEGORY_TENCENT_DOCS, files, TARGET_PATH))
        justRun { action.onDealFile(any(), any(), any()) }
        justRun { action.notifyObserver(any(), any()) }
        val result = action.syncDownload(item)
        Assert.assertTrue(result)
        verify { action.onDealFile(any(), any(), any()) }
        unmockkStatic("com.filemanager.common.utils.StringResourcesKt")
    }

    companion object {
        private const val TARGET_PATH = "/storage/emulated/0/Download/"
        private const val CLOUD_DOCS_ITEM_ID = "300000000GenTvrroVqZI"
        private const val CLOUD_DOCS_ITEM_TITLE = "文件管理测试经验分享.pptx"
        private const val CLOUD_DOCS_ITEM_TYPE = "slide"
        private const val CLOUD_DOCS_ITEM_URL = "https://docs.qq.com/document/DR2VuVHZycm9WcVpJ"
    }
}