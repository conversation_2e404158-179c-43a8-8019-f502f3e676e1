/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionSave.kt
 ** Description: Save files function
 ** Version: 1.0
 ** Date: 2023/6/5
 ** Author: hank.zhou(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.save

import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.FileOperateUtil
import com.filemanager.fileoperate.NotifyMediaScannerBatchAction
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.filemanager.fileoperate.copy.ERROR_COPY
import com.filemanager.fileoperate.copy.ERROR_PATH_NULL
import com.filemanager.fileoperate.copy.ERROR_STORAGE_NOT_ENOUGH_SAVE
import com.filemanager.fileoperate.copy.FileActionCopy
import com.filemanager.fileoperate.copy.FileCopyHelper
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_COPY
import java.io.File

class FileActionSave : FileActionCopy {

    private val mMediaScannerBatchAction by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_SAVE)
    }
    constructor(
        lifecycle: LifecycleOwner,
        sourceFiles: List<out BaseFileBean>,
        destFile: BaseFileBean
    ) : super(lifecycle, sourceFiles, destFile)

    override fun exceptionDetection(): Any? {
        // calculate file total size
        for (file in mOperateFiles) {
            mTotalFileLength += file.mSize
        }
        Log.d(TAG, "exceptionDetection: totalLength:$mTotalFileLength")

        if (mTotalFileLength < 0) {
            return ERROR_PATH_NULL
        }

        // Check the empty size of the dest storage
        val storageState = checkDestStorageSpace(mDestFile, mTotalFileLength)
        return if (storageState.first) {
            Log.d(TAG, "exceptionDetection: storage is not enough")
            val notice = when (KtUtils.getStorageByPath(mContext, mDestFile.mData)) {
                            KtUtils.STORAGE_INTERNAL, KtUtils.STORAGE_INTERNAL_MULTI_APP -> {
                                mContext.getString(com.filemanager.common.R.string.phone_storage_can_not_save)
                            }
                            KtUtils.STORAGE_EXTERNAL -> mContext.getString(com.filemanager.common.R.string.sd_storage_can_not_save)
                            else -> mContext.getString(com.filemanager.common.R.string.otg_storage_can_not_save)
                        }
            Pair(ERROR_STORAGE_NOT_ENOUGH_SAVE, notice)
        } else null
    }

    override fun onSkipDealFile(sourceFile: File) {
        mHasSkipFile = true
        for (bean in mOperateFiles) {
            if (bean.virtualFileHashCode != null && sourceFile.absolutePath.contains(bean.virtualFileHashCode!!)) {
                mDealFileListener.addProgress(bean.mSize)
                break
            }
        }
    }

    override fun onDealFile(sourceFile: File, destFile: File, isDirectory: Boolean) {
        //FileCopyHelper.copyFile(sourceFile, destFile, isDirectory, mDealFileListener)
        Log.d(TAG, "onDealFile mOperateFiles[0].mLocalFileUri = ${mOperateFiles[0].mLocalFileUri}")
        for (bean in mOperateFiles) {
            if (bean.virtualFileHashCode != null && sourceFile.absolutePath.contains(bean.virtualFileHashCode!!)) {
                if (bean.isString) {
                    FileOperateUtil.saveText(bean.stringData!!, destFile.toPath(), mDealFileListener)
                } else {
                    FileCopyHelper.copyToFileByUri(bean.mLocalFileUri!!, destFile.toPath(), true, mDealFileListener)
                }
                break
            }
        }
        if (mIsOperateDatabase) {
            mMediaScannerBatchAction.add(destFile.absolutePath)
        }
    }

    override fun onDealAllFilesEnd() {
        if (mIsOperateDatabase) {
            mMediaScannerBatchAction.flush()
        }
        Log.d(TAG, "onDealAllFilesEnd mHasSuccessFile = $mHasSuccessFile")
        if (mHasSuccessFile) {
            notifyObserver(ACTION_DONE, mDestFile.mData)
            val fileServiceAction = Injector.injectFactory<IFileService>()
            fileServiceAction?.syncOperate(OPERATE_TYPE_COPY, hashSetOf(mDestFile.mData))
        } else {
            notifyObserver(ACTION_FAILED, ERROR_COPY)
        }
    }

    override fun onDealFileResume() {
        notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                mContext.getString(com.filemanager.common.R.string.copy_file_dialog_title), false, mCurrentProgress
            ))
    }

    override fun onDealFileSuccess(sourceFile: File, destFile: File, isDirectory: Boolean) {
        mHasSuccessFile = true
    }
}