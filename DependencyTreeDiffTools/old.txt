 \--- project :Ad:Overseas
- \--- com.opos.ad:overseas-ad-native-entry-global-pub:2.14.1
-      +--- com.opos.ad:overseas-ad-cmn-base:2.14.1
-      |    +--- com.github.bumptech.glide:glide:4.12.0 (*)
-      |    +--- androidx.constraintlayout:constraintlayout:2.1.4 (*)
-      |    +--- androidx.cardview:cardview:1.0.0 (*)
-      |    +--- com.opos.ad:overseas-ad-ov-cmn-base:2.14.1
-      |    |    +--- com.opos.ad:cmn-lib-client-id:1.2.0
-      |    |    |    \--- com.opos.ad:cmn-lib-custom-policy:1.1.0
-      |    |    |         \--- com.opos.ad:cmn-lib-logan:2.5.0
-      |    |    |              +--- com.oplus.stdid.sdk:sdk:1.0.4 -> 1.0.8
-      |    |    |              +--- com.opos.ad:cmn-lib-ext:1.0.2
-      |    |    |              \--- com.oplus:log:4.0.6 -> 4.0.9.5
-      |    |    |                   +--- com.usertrace.cdo:cdo-usertrace-domain:1.1.0-releases
-      |    |    |                   \--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.6.0 (*)
-      |    |    +--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
-      |    |    +--- com.opos.ad:cmn-lib-io:1.3.0
-      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.5.0 (*)
-      |    |    |    \--- com.opos.ad:cmn-lib-ext:1.0.2
-      |    |    +--- com.opos.ad:cmn-lib-dvc-info:1.6.0
-      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.5.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-lib-ext:1.0.2
-      |    |    |    +--- com.opos.ad:cmn-lib-crypt:1.2.1
-      |    |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.2 -> 2.5.0 (*)
-      |    |    |    |    \--- com.opos.ad:cmn-lib-ext:1.0.2
-      |    |    |    \--- com.opos.ad:cmn-lib-custom-policy:1.1.0 (*)
-      |    |    +--- com.opos.ad:cmn-lib-sys-svc:1.11.0
-      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.5.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-lib-ext:1.0.2
-      |    |    |    \--- com.opos.ad:cmn-lib-crypt:1.2.1 (*)
-      |    |    +--- com.opos.ad:cmn-lib-tp:1.0.3 -> 1.1.0
-      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
-      |    |    |    \--- com.opos.ad:cmn-lib-threadpool:1.0.5
-      |    |    |         +--- com.opos.ad:cmn-lib-logan:1.2.0 -> 2.5.0 (*)
-      |    |    |         \--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
-      |    |    +--- com.opos.ad:cmn-lib-logan:2.5.0 (*)
-      |    |    +--- com.opos.ad:cmn-lib-crypt:1.1.0 -> 1.2.1 (*)
-      |    |    +--- com.opos.ad:so-biz-crypt:1.6.0
-      |    |    |    \--- com.opos.ad:cmn-lib-logan:1.3.0 -> 2.5.0 (*)
-      |    |    +--- com.opos.ad:cmn-biz-ext:1.2.1 -> 1.4.0
-      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
-      |    |    |    +--- com.opos.ad:cmn-lib-dvc-info:1.2.0 -> 1.6.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-lib-sys-svc:1.9.0 -> 1.11.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-third-id-normal:2.9.0 -> 3.2.0
-      |    |    |    |    +--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
-      |    |    |    |    +--- com.oplus.stdid.sdk:sdk:1.0.4 -> 1.0.8
-      |    |    |    |    +--- com.opos.ad:cmn-lib-client-id:1.0.0 -> 1.2.0 (*)
-      |    |    |    |    \--- com.opos.ad:cmn-lib-logan:2.4.0 -> 2.5.0 (*)
-      |    |    |    \--- com.heytap.nearx:routedata:1.0.0
-      |    |    |         +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.50 -> 1.8.0 (*)
-      |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.50 -> 1.8.22 (*)
-      |    |    +--- com.opos.ad:cmn-third-id-normal:3.2.0 (*)
-      |    |    +--- com.opos.ad:cmn-lib-zip:1.0.6 -> 1.1.0
-      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
-      |    |    |    \--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
-      |    |    +--- com.opos.ad:cmn-func-mix-net-pub:1.8.0
-      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-biz-ext:1.4.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-third-id-normal:2.9.0 -> 3.2.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-lib-net:1.1.0
-      |    |    |    |    +--- com.opos.ad:cmn-lib-sys-svc:1.9.0 -> 1.11.0 (*)
-      |    |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
-      |    |    |    |    \--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
-      |    |    |    +--- com.opos.ad:cmn-lib-tp:1.1.0 (*)
-      |    |    |    +--- com.opos.ad:so-biz-crypt:1.6.0 (*)
-      |    |    |    \--- com.opos.ad:cmn-lib-zip:1.1.0 (*)
-      |    |    +--- com.google.code.gson:gson:2.10.1
-      |    |    +--- androidx.core:core-ktx:1.6.0 -> 1.12.0 (*)
-      |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.2 -> 1.7.3 (*)
-      |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2 -> 1.7.3 (*)
-      |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      |    +--- com.opos.ad:overseas-ad-strategy-proto:2.14.1
-      |    |    +--- com.squareup.okio:okio:2.6.0 -> 3.4.0 (*)
-      |    |    +--- com.squareup.wire:wire-runtime:3.0.0
-      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.50 -> 1.8.22 (*)
-      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.3.50 -> 1.8.22
-      |    |    |    \--- com.squareup.okio:okio-multiplatform:2.4.1
-      |    |    |         \--- com.squareup.okio:okio:2.4.1 -> 3.4.0 (*)
-      |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      +--- com.opos.ad:overseas-ad-third-native-global:2.14.1
-      |    +--- com.google.android.gms:play-services-ads:22.3.0
-      |    |    +--- androidx.browser:browser:1.4.0
-      |    |    |    +--- androidx.collection:collection:1.1.0 (*)
-      |    |    |    +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
-      |    |    |    +--- androidx.interpolator:interpolator:1.0.0 (*)
-      |    |    |    +--- androidx.core:core:1.1.0 -> 1.12.0 (*)
-      |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
-      |    |    |    \--- com.google.guava:listenablefuture:1.0
-      |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
-      |    |    +--- androidx.core:core:1.0.0 -> 1.12.0 (*)
-      |    |    +--- com.google.android.gms:play-services-ads-base:22.3.0
-      |    |    |    \--- com.google.android.gms:play-services-basement:18.0.0 -> 18.2.0
-      |    |    |         +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
-      |    |    |         +--- androidx.core:core:1.2.0 -> 1.12.0 (*)
-      |    |    |         \--- androidx.fragment:fragment:1.0.0 -> 1.4.1 (*)
-      |    |    +--- com.google.android.gms:play-services-ads-identifier:18.0.0 -> 18.0.1
-      |    |    |    \--- com.google.android.gms:play-services-basement:18.0.0 -> 18.2.0 (*)
-      |    |    +--- com.google.android.gms:play-services-ads-lite:22.3.0
-      |    |    |    +--- androidx.work:work-runtime:2.7.0
-      |    |    |    |    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.3.0 (*)
-      |    |    |    |    +--- com.google.guava:listenablefuture:1.0
-      |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.1.0 -> 2.6.2 (*)
-      |    |    |    |    +--- androidx.startup:startup-runtime:1.0.0 -> 1.1.1 (*)
-      |    |    |    |    +--- androidx.core:core:1.6.0 -> 1.12.0 (*)
-      |    |    |    |    +--- androidx.room:room-runtime:2.2.5 -> 2.4.3 (*)
-      |    |    |    |    +--- androidx.sqlite:sqlite:2.1.0 -> 2.2.0 (*)
-      |    |    |    |    +--- androidx.sqlite:sqlite-framework:2.1.0 -> 2.2.0 (*)
-      |    |    |    |    +--- androidx.core:core:1.1.0 -> 1.12.0 (*)
-      |    |    |    |    \--- androidx.lifecycle:lifecycle-service:2.1.0 -> 2.6.2
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.6.2 (*)
-      |    |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.8.22 (*)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-common:2.6.2 (c)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata:2.6.2 (c)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.6.2 (c)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2 (c)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.2 (c)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-process:2.6.2 (c)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-runtime:2.6.2 (c)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.2 (c)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel:2.6.2 (c)
-      |    |    |    |         +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2 (c)
-      |    |    |    |         \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2 (c)
-      |    |    |    +--- com.google.android.gms:play-services-ads-base:22.3.0 (*)
-      |    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.2.0 (*)
-      |    |    |    +--- com.google.android.gms:play-services-measurement-sdk-api:20.1.2
-      |    |    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.2.0 (*)
-      |    |    |    |    \--- com.google.android.gms:play-services-measurement-base:20.1.2
-      |    |    |    |         \--- com.google.android.gms:play-services-basement:18.0.0 -> 18.2.0 (*)
-      |    |    |    \--- com.google.android.ump:user-messaging-platform:2.0.0
-      |    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.6.0 (*)
-      |    |    |         +--- com.google.android.gms:play-services-ads-identifier:17.0.0 -> 18.0.1 (*)
-      |    |    |         \--- com.google.android.gms:play-services-basement:17.0.0 -> 18.2.0 (*)
-      |    |    +--- com.google.android.gms:play-services-appset:16.0.1 -> 16.0.2
-      |    |    |    +--- com.google.android.gms:play-services-base:18.0.1 -> 18.1.0
-      |    |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
-      |    |    |    |    +--- androidx.core:core:1.2.0 -> 1.12.0 (*)
-      |    |    |    |    +--- androidx.fragment:fragment:1.0.0 -> 1.4.1 (*)
-      |    |    |    |    +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.2.0 (*)
-      |    |    |    |    \--- com.google.android.gms:play-services-tasks:18.0.2
-      |    |    |    |         \--- com.google.android.gms:play-services-basement:18.1.0 -> 18.2.0 (*)
-      |    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.2.0 (*)
-      |    |    |    \--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.0.2 (*)
-      |    |    +--- com.google.android.gms:play-services-basement:18.0.0 -> 18.2.0 (*)
-      |    |    \--- com.google.android.gms:play-services-tasks:18.0.1 -> 18.0.2 (*)
-      |    +--- com.google.android.gms:play-services-basement:18.2.0 (*)
-      |    +--- com.opos.ad:overseas-ad-strategy-proto:2.14.1 (*)
-      |    +--- com.opos.ad:overseas-ad-cmn-base:2.14.1 (*)
-      |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      |    +--- com.facebook.android:audience-network-sdk:6.16.0
-      |    |    \--- com.google.android.gms:play-services-basement:11.0.4 -> 18.2.0 (*)
-      |    \--- com.vungle:publisher-sdk-android:6.12.1
-      |         +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
-      |         +--- androidx.core:core:1.1.0 -> 1.12.0 (*)
-      |         +--- com.google.code.gson:gson:2.8.6 -> 2.10.1
-      |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0 (*)
-      |         +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.11.0 (*)
-      |         \--- com.squareup.okio:okio:1.17.3 -> 3.4.0 (*)
-      +--- com.opos.ad:overseas-ad-strategy-global:2.14.1
-      |    +--- com.squareup.okio:okio:2.6.0 -> 3.4.0 (*)
-      |    +--- com.squareup.wire:wire-runtime:3.0.0 (*)
-      |    +--- com.opos.ad:overseas-ad-strategy-proto:2.14.1 (*)
-      |    +--- com.opos.ad:overseas-ad-cmn-base:2.14.1 (*)
-      |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      +--- com.opos.ad:stobus-oaPub:1.0.1
-      |    +--- com.opos.ad:cmn-func-mix-net-pub:1.7.0 -> 1.8.0 (*)
-      |    +--- com.opos.ad:cmn-lib-custom-policy:1.0.0 -> 1.1.0 (*)
-      |    +--- com.opos.ad:cmn-lib-logan:2.4.0 -> 2.5.0 (*)
-      |    +--- com.opos.ad:st-lib-st-oa:1.0.1
-      |    |    +--- com.opos.ad:cmn-func-mix-net-pub:1.7.0 -> 1.8.0 (*)
-      |    |    +--- com.opos.ad:cmn-lib-tp:1.1.0 (*)
-      |    |    +--- com.opos.ad:cmn-biz-ext:1.4.0 (*)
-      |    |    +--- com.opos.ad:cmn-lib-dvc-info:1.3.0 -> 1.6.0 (*)
-      |    |    +--- com.opos.ad:so-biz-crypt:1.6.0 (*)
-      |    |    +--- com.opos.ad:cmn-biz-stobus-strategy:1.0.1
-      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.4.0 -> 2.5.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-func-mix-net-pub:1.7.0 -> 1.8.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-lib-tp:1.1.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-lib-dvc-info:1.3.0 -> 1.6.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-lib-crypt:1.2.0 -> 1.2.1 (*)
-      |    |    |    +--- com.opos.ad:so-biz-crypt:1.6.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-third-id-normal:3.2.0 (*)
-      |    |    |    +--- com.opos.ad:cmn-biz-ext:1.4.0 (*)
-      |    |    |    \--- com.opos.ad:cmn-biz-stobus-strategy-env:1.0.1
-      |    |    |         +--- com.opos.ad:cmn-biz-ext:1.4.0 (*)
-      |    |    |         +--- com.opos.ad:cmn-lib-dvc-info:1.3.0 -> 1.6.0 (*)
-      |    |    |         +--- com.opos.ad:cmn-lib-logan:2.4.0 -> 2.5.0 (*)
-      |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      |    +--- com.opos.ad:st-lib_obus-oa:1.0.1
-      |    |    +--- com.heytap.nearx:cloudconfig:******* -> 2.4.2.4
-      |    |    |    +--- com.heytap.nearx:utils:1.0.8.security
-      |    |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.50 -> 1.8.0 (*)
-      |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.50 -> 1.8.22 (*)
-      |    |    |    +--- com.heytap.nearx:cloudconfig-proto-wire:2.4.2.4
-      |    |    |    |    +--- com.heytap.nearx.protobuff:wire-runtime-isolate:2.2.0.222
-      |    |    |    |    \--- com.squareup.okio:okio:2.5.0 -> 3.4.0 (*)
-      |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.72 -> 1.8.0 (*)
-      |    |    |    \--- com.heytap.nearx:exception-collector:1.0.13
-      |    |    |         +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
-      |    |    |         \--- com.heytap.nearx:visualize-assist:1.0.13
-      |    |    |              \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
-      |    |    +--- com.heytap.nearx:cloudconfig-area:******* -> 2.4.2.4
-      |    |    |    +--- com.heytap.nearx:cloudconfig:2.4.2.4 (*)
-      |    |    |    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.72 -> 1.8.0 (*)
-      |    |    +--- com.oplus.stdid.sdk:sdk:1.0.6 -> 1.0.8
-      |    |    +--- com.heytap.nearx:common:***********
-      |    |    |    +--- com.heytap.nearx:cloudconfig-area:2.3.9 -> 2.4.2.4 (*)
-      |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.4.0 -> 1.8.0 (*)
-      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.22 (*)
-      |    |    |    +--- com.oplus.stdid.sdk:sdk:1.0.4 -> 1.0.8
-      |    |    |    \--- com.heytap.nearx:database:1.0.22 -> 1.0.23
-      |    |    |         +--- com.heytap.nearx:utils:1.0.7 -> 1.0.8.security (*)
-      |    |    |         +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.72 -> 1.8.0 (*)
-      |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.72 -> 1.8.22 (*)
-      |    |    |         +--- android.arch.persistence:db:1.1.1 -> androidx.sqlite:sqlite:2.2.0 (*)
-      |    |    |         \--- android.arch.persistence:db-framework:1.1.1 -> androidx.sqlite:sqlite-framework:2.2.0 (*)
-      |    |    +--- com.heytap.nearx:httpdns:***********
-      |    |    |    +--- com.heytap.nearx:cloudconfig-area:2.3.9 -> 2.4.2.4 (*)
-      |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.4.0 -> 1.8.0 (*)
-      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.22 (*)
-      |    |    |    +--- com.heytap.nearx:exception-collector:1.0.12 -> 1.0.13 (*)
-      |    |    |    \--- com.heytap.nearx:database:1.0.22 -> 1.0.23 (*)
-      |    |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.11.0 (*)
-      |    |    +--- com.oplus:log:4.0.9.5 (*)
-      |    |    +--- com.oplus.nearx:track:3.4.16
-      |    |    |    +--- androidx.multidex:multidex:2.0.0
-      |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
-      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.61 -> 1.8.0 (*)
-      |    |    |    +--- com.heytap.nearx:database:1.0.23 (*)
-      |    |    |    +--- com.heytap.nearx:utils:1.0.8.security (*)
-      |    |    |    +--- com.oplus.nearx:exception-collector:3.4.16
-      |    |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
-      |    |    |    |    \--- com.oplus.nearx:visualize-assist:3.4.16
-      |    |    |    |         \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
-      |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
-      |    |    |    \--- com.oplus.nearx:visualize-assist:3.4.16 (*)
-      |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      |    |    +--- com.heytap.nearx:taphttp-env-oversea:***********
-      |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.22 (*)
-      |    |    \--- com.oplus:log-domain-oversea:4.0.9.5
-      |    +--- com.opos.ad:cmn-biz-stobus-strategy:1.0.1 (*)
-      |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-      |    \--- com.oplus:log-domain-oversea:4.0.5 -> 4.0.9.5
-      \--- com.opos.ad:overseas-ad-mix-ad-global-pub:2.14.1
-           +--- com.opos.ad:overseas-ad-cmn-base:2.14.1 (*)
-           +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-           +--- com.google.ads.interactivemedia.v3:interactivemedia:3.29.0
-           |    +--- com.google.android.gms:play-services-ads-identifier:18.0.1 (*)
-           |    +--- com.google.android.gms:play-services-appset:16.0.2 (*)
-           |    +--- com.google.android.gms:play-services-base:18.1.0 (*)
-           |    +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.2.0 (*)
-           |    +--- com.google.android.gms:play-services-tasks:18.0.2 (*)
-           |    +--- androidx.annotation:annotation:1.5.0 -> 1.6.0 (*)
-           |    +--- androidx.browser:browser:1.4.0 (*)
-           |    +--- androidx.preference:preference:1.2.0 (*)
-           |    \--- androidx.webkit:webkit:1.5.0
-           |         +--- androidx.annotation:annotation:1.2.0 -> 1.6.0 (*)
-           |         \--- androidx.core:core:1.1.0 -> 1.12.0 (*)
-           \--- com.opos.ad:overseas-ad-strategy-global:2.14.1 (*)
update com.opos.ad:overseas-ad-global-pub:2.14.1   >>>>>>  ********
+ \--- com.opos.ad:overseas-ad-native-entry-global-pub:********
+      +--- com.opos.ad:overseas-ad-cmn-base:********
+      |    +--- androidx.constraintlayout:constraintlayout:2.1.4 (*)
+      |    +--- androidx.cardview:cardview:1.0.0 (*)
+      |    +--- com.github.bumptech.glide:glide:4.12.0 (*)
+      |    +--- com.opos.ad:overseas-ad-ov-cmn-base:********
+      |    |    +--- com.opos.ad:cmn-lib-client-id:1.2.0
+      |    |    |    \--- com.opos.ad:cmn-lib-custom-policy:1.1.0
+      |    |    |         \--- com.opos.ad:cmn-lib-logan:2.5.0
+      |    |    |              +--- com.oplus.stdid.sdk:sdk:1.0.4 -> 1.0.8
+      |    |    |              +--- com.opos.ad:cmn-lib-ext:1.0.2
+      |    |    |              \--- com.oplus:log:4.0.6 -> *******
+      |    |    |                   +--- com.usertrace.cdo:cdo-usertrace-domain:1.1.0-releases
+      |    |    |                   \--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.6.0 (*)
+      |    |    +--- com.opos.ad:cmn-lib-ext:1.0.2
+      |    |    +--- com.opos.ad:cmn-lib-io:1.3.0
+      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.5.0 (*)
+      |    |    |    \--- com.opos.ad:cmn-lib-ext:1.0.2
+      |    |    +--- com.opos.ad:cmn-lib-dvc-info:1.6.0
+      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.5.0 (*)
+      |    |    |    +--- com.opos.ad:cmn-lib-ext:1.0.2
+      |    |    |    +--- com.opos.ad:cmn-lib-crypt:1.2.1
+      |    |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.2 -> 2.5.0 (*)
+      |    |    |    |    \--- com.opos.ad:cmn-lib-ext:1.0.2
+      |    |    |    \--- com.opos.ad:cmn-lib-custom-policy:1.1.0 (*)
+      |    |    +--- com.opos.ad:cmn-lib-sys-svc:1.11.0
+      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.5.0 (*)
+      |    |    |    +--- com.opos.ad:cmn-lib-ext:1.0.2
+      |    |    |    \--- com.opos.ad:cmn-lib-crypt:1.2.1 (*)
+      |    |    +--- com.opos.ad:cmn-lib-tp:1.1.0
+      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
+      |    |    |    \--- com.opos.ad:cmn-lib-threadpool:1.0.5
+      |    |    |         +--- com.opos.ad:cmn-lib-logan:1.2.0 -> 2.5.0 (*)
+      |    |    |         \--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
+      |    |    +--- com.opos.ad:cmn-lib-logan:2.5.0 (*)
+      |    |    +--- com.oplus:log:******* (*)
+      |    |    +--- com.oplus:log-domain-oversea:*******
+      |    |    +--- com.opos.ad:cmn-lib-crypt:1.2.1 (*)
+      |    |    +--- com.opos.ad:so-biz-crypt:1.6.0
+      |    |    |    \--- com.opos.ad:cmn-lib-logan:1.3.0 -> 2.5.0 (*)
+      |    |    +--- com.opos.ad:cmn-biz-ext:1.4.0
+      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
+      |    |    |    +--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
+      |    |    |    +--- com.opos.ad:cmn-lib-dvc-info:1.2.0 -> 1.6.0 (*)
+      |    |    |    +--- com.opos.ad:cmn-lib-sys-svc:1.9.0 -> 1.11.0 (*)
+      |    |    |    +--- com.opos.ad:cmn-third-id-normal:2.9.0 -> 3.2.0
+      |    |    |    |    +--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
+      |    |    |    |    +--- com.oplus.stdid.sdk:sdk:1.0.4 -> 1.0.8
+      |    |    |    |    +--- com.opos.ad:cmn-lib-client-id:1.0.0 -> 1.2.0 (*)
+      |    |    |    |    \--- com.opos.ad:cmn-lib-logan:2.4.0 -> 2.5.0 (*)
+      |    |    |    \--- com.heytap.nearx:routedata:1.0.0
+      |    |    |         +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.50 -> 1.8.0 (*)
+      |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.50 -> 1.8.22 (*)
+      |    |    +--- com.opos.ad:cmn-third-id-normal:3.2.0 (*)
+      |    |    +--- com.opos.ad:cmn-lib-zip:1.1.0
+      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
+      |    |    |    \--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
+      |    |    +--- com.opos.ad:cmn-func-mix-net-pub:1.8.0
+      |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
+      |    |    |    +--- com.opos.ad:cmn-biz-ext:1.4.0 (*)
+      |    |    |    +--- com.opos.ad:cmn-third-id-normal:2.9.0 -> 3.2.0 (*)
+      |    |    |    +--- com.opos.ad:cmn-lib-net:1.1.0
+      |    |    |    |    +--- com.opos.ad:cmn-lib-sys-svc:1.9.0 -> 1.11.0 (*)
+      |    |    |    |    +--- com.opos.ad:cmn-lib-logan:2.3.0 -> 2.5.0 (*)
+      |    |    |    |    \--- com.opos.ad:cmn-lib-ext:1.0.1 -> 1.0.2
+      |    |    |    +--- com.opos.ad:cmn-lib-tp:1.1.0 (*)
+      |    |    |    +--- com.opos.ad:so-biz-crypt:1.6.0 (*)
+      |    |    |    \--- com.opos.ad:cmn-lib-zip:1.1.0 (*)
+      |    |    +--- com.google.code.gson:gson:2.10.1
+      |    |    +--- androidx.core:core-ktx:1.6.0 -> 1.12.0 (*)
+      |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.2 -> 1.7.3 (*)
+      |    |    +--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.2 -> 1.7.3 (*)
+      |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      |    +--- com.opos.ad:overseas-ad-strategy-proto:********
+      |    |    +--- com.squareup.okio:okio:2.6.0 -> 3.4.0 (*)
+      |    |    +--- com.squareup.wire:wire-runtime:3.0.0
+      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.50 -> 1.8.22 (*)
+      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.3.50 -> 1.8.22
+      |    |    |    \--- com.squareup.okio:okio-multiplatform:2.4.1
+      |    |    |         \--- com.squareup.okio:okio:2.4.1 -> 3.4.0 (*)
+      |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      +--- com.opos.ad:overseas-ad-third-native-global:********
+      |    +--- com.applovin:applovin-sdk:12.2.0
+      |    |    +--- com.google.android.gms:play-services-ads-identifier:17.1.0 -> 18.0.0
+      |    |    |    \--- com.google.android.gms:play-services-basement:18.0.0
+      |    |    |         +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
+      |    |    |         +--- androidx.core:core:1.2.0 -> 1.12.0 (*)
+      |    |    |         \--- androidx.fragment:fragment:1.0.0 -> 1.4.1 (*)
+      |    |    +--- com.google.android.gms:play-services-appset:16.0.0 -> 16.0.1
+      |    |    |    +--- com.google.android.gms:play-services-base:18.0.0
+      |    |    |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
+      |    |    |    |    +--- androidx.core:core:1.2.0 -> 1.12.0 (*)
+      |    |    |    |    +--- androidx.fragment:fragment:1.0.0 -> 1.4.1 (*)
+      |    |    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 (*)
+      |    |    |    |    \--- com.google.android.gms:play-services-tasks:18.0.0 -> 18.0.1
+      |    |    |    |         \--- com.google.android.gms:play-services-basement:18.0.0 (*)
+      |    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 (*)
+      |    |    |    \--- com.google.android.gms:play-services-tasks:18.0.0 -> 18.0.1 (*)
+      |    |    \--- androidx.browser:browser:1.4.0
+      |    |         +--- androidx.collection:collection:1.1.0 (*)
+      |    |         +--- androidx.concurrent:concurrent-futures:1.0.0 -> 1.1.0 (*)
+      |    |         +--- androidx.interpolator:interpolator:1.0.0 (*)
+      |    |         +--- androidx.core:core:1.1.0 -> 1.12.0 (*)
+      |    |         +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
+      |    |         \--- com.google.guava:listenablefuture:1.0
+      |    +--- com.google.android.gms:play-services-ads:22.3.0
+      |    |    +--- androidx.browser:browser:1.4.0 (*)
+      |    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
+      |    |    +--- androidx.core:core:1.0.0 -> 1.12.0 (*)
+      |    |    +--- com.google.android.gms:play-services-ads-base:22.3.0
+      |    |    |    \--- com.google.android.gms:play-services-basement:18.0.0 (*)
+      |    |    +--- com.google.android.gms:play-services-ads-identifier:18.0.0 (*)
+      |    |    +--- com.google.android.gms:play-services-ads-lite:22.3.0
+      |    |    |    +--- androidx.work:work-runtime:2.7.0 -> 2.8.0
+      |    |    |    |    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.3.0 (*)
+      |    |    |    |    +--- androidx.core:core:1.1.0 -> 1.12.0 (*)
+      |    |    |    |    +--- androidx.core:core:1.6.0 -> 1.12.0 (*)
+      |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.1.0 -> 2.6.2 (*)
+      |    |    |    |    +--- androidx.lifecycle:lifecycle-service:2.1.0 -> 2.6.2
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 (*)
+      |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.10 -> 1.8.22 (*)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-common:2.6.2 (c)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.6.2 (c)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.6.2 (c)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2 (c)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-livedata-ktx:2.6.2 (c)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-process:2.6.2 (c)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime:2.6.2 (c)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-runtime-ktx:2.6.2 (c)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.6.2 (c)
+      |    |    |    |    |    +--- androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2 (c)
+      |    |    |    |    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2 (c)
+      |    |    |    |    +--- androidx.room:room-runtime:2.5.0 (*)
+      |    |    |    |    +--- androidx.sqlite:sqlite-framework:2.3.0 (*)
+      |    |    |    |    +--- androidx.startup:startup-runtime:1.0.0 -> 1.1.1 (*)
+      |    |    |    |    +--- com.google.guava:listenablefuture:1.0
+      |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.7.21 -> 1.8.22 (*)
+      |    |    |    +--- com.google.android.gms:play-services-ads-base:22.3.0 (*)
+      |    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 (*)
+      |    |    |    +--- com.google.android.gms:play-services-measurement-sdk-api:20.1.2
+      |    |    |    |    +--- com.google.android.gms:play-services-basement:18.0.0 (*)
+      |    |    |    |    \--- com.google.android.gms:play-services-measurement-base:20.1.2
+      |    |    |    |         \--- com.google.android.gms:play-services-basement:18.0.0 (*)
+      |    |    |    \--- com.google.android.ump:user-messaging-platform:2.0.0
+      |    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.6.0 (*)
+      |    |    |         +--- com.google.android.gms:play-services-ads-identifier:17.0.0 -> 18.0.0 (*)
+      |    |    |         \--- com.google.android.gms:play-services-basement:17.0.0 -> 18.0.0 (*)
+      |    |    +--- com.google.android.gms:play-services-appset:16.0.1 (*)
+      |    |    +--- com.google.android.gms:play-services-basement:18.0.0 (*)
+      |    |    \--- com.google.android.gms:play-services-tasks:18.0.1 (*)
+      |    +--- androidx.work:work-runtime:2.8.0 (*)
+      |    +--- com.opos.ad:overseas-ad-strategy-proto:******** (*)
+      |    +--- com.opos.ad:overseas-ad-cmn-base:******** (*)
+      |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      |    +--- com.facebook.android:audience-network-sdk:6.16.0
+      |    |    \--- com.google.android.gms:play-services-basement:11.0.4 -> 18.0.0 (*)
+      |    \--- com.vungle:publisher-sdk-android:6.12.1
+      |         +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
+      |         +--- androidx.core:core:1.1.0 -> 1.12.0 (*)
+      |         +--- com.google.code.gson:gson:2.8.6 -> 2.10.1
+      |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0 (*)
+      |         +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.11.0 (*)
+      |         \--- com.squareup.okio:okio:1.17.3 -> 3.4.0 (*)
+      +--- com.opos.ad:overseas-ad-strategy-global:********
+      |    +--- com.squareup.okio:okio:2.6.0 -> 3.4.0 (*)
+      |    +--- com.squareup.wire:wire-runtime:3.0.0 (*)
+      |    +--- com.heytap.nearx:cloudconfig:*******
+      |    |    +--- com.heytap.nearx:utils:1.0.8.security
+      |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.50 -> 1.8.0 (*)
+      |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.50 -> 1.8.22 (*)
+      |    |    +--- com.heytap.nearx:cloudconfig-proto-wire:*******
+      |    |    |    +--- com.heytap.nearx.protobuff:wire-runtime-isolate:2.2.0.222
+      |    |    |    \--- com.squareup.okio:okio:2.5.0 -> 3.4.0 (*)
+      |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.72 -> 1.8.0 (*)
+      |    |    \--- com.heytap.nearx:exception-collector:1.0.13
+      |    |         +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
+      |    |         \--- com.heytap.nearx:visualize-assist:1.0.13
+      |    |              \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
+      |    +--- com.heytap.nearx:cloudconfig-area:*******
+      |    |    +--- com.heytap.nearx:cloudconfig:******* (*)
+      |    |    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.72 -> 1.8.0 (*)
+      |    +--- com.opos.ad:overseas-ad-strategy-proto:******** (*)
+      |    +--- com.opos.ad:overseas-ad-cmn-base:******** (*)
+      |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      +--- com.opos.ad:stobus-oaPub:1.0.3
+      |    +--- com.opos.ad:cmn-lib-custom-policy:1.0.0 -> 1.1.0 (*)
+      |    +--- com.opos.ad:stobus-st:1.0.3
+      |    |    +--- com.opos.ad:stobus-strategy:1.0.3
+      |    |    |    \--- com.opos.ad:stobus-strategy-env:1.0.3
+      |    |    |         +--- com.opos.ad:stobus-base:1.0.3
+      |    |    |         |    +--- com.opos.ad:cmn-lib-client-id:1.2.0 (*)
+      |    |    |         |    +--- com.opos.ad:cmn-lib-ext:1.0.2
+      |    |    |         |    +--- com.opos.ad:cmn-lib-io:1.3.0 (*)
+      |    |    |         |    +--- com.opos.ad:cmn-lib-dvc-info:1.6.0 (*)
+      |    |    |         |    +--- com.opos.ad:cmn-lib-sys-svc:1.11.0 (*)
+      |    |    |         |    +--- com.opos.ad:cmn-lib-tp:1.1.0 (*)
+      |    |    |         |    +--- com.opos.ad:cmn-lib-logan:2.5.0 (*)
+      |    |    |         |    +--- com.oplus:log:******* (*)
+      |    |    |         |    +--- com.oplus:log-domain-oversea:*******
+      |    |    |         |    +--- com.opos.ad:cmn-lib-crypt:1.2.1 (*)
+      |    |    |         |    +--- com.opos.ad:so-biz-crypt:1.6.0 (*)
+      |    |    |         |    +--- com.opos.ad:cmn-biz-ext:1.4.0 (*)
+      |    |    |         |    +--- com.opos.ad:cmn-third-id-normal:3.2.0 (*)
+      |    |    |         |    +--- com.opos.ad:cmn-lib-zip:1.1.0 (*)
+      |    |    |         |    +--- com.opos.ad:cmn-func-mix-net-pub:1.8.0 (*)
+      |    |    |         |    +--- com.google.code.gson:gson:2.10.1
+      |    |    |         |    +--- com.heytap.nearx:cloudconfig:******* -> ******* (*)
+      |    |    |         |    +--- com.heytap.nearx:cloudconfig-area:******* -> ******* (*)
+      |    |    |         |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      |    +--- com.opos.ad:stobus-obus:1.0.3
+      |    |    +--- com.oplus.stdid.sdk:sdk:1.0.6 -> 1.0.8
+      |    |    +--- com.heytap.nearx:common:***********
+      |    |    |    +--- com.heytap.nearx:cloudconfig-area:2.3.9 -> ******* (*)
+      |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.4.0 -> 1.8.0 (*)
+      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.22 (*)
+      |    |    |    +--- com.oplus.stdid.sdk:sdk:1.0.4 -> 1.0.8
+      |    |    |    \--- com.heytap.nearx:database:1.0.22 -> 1.0.23
+      |    |    |         +--- com.heytap.nearx:utils:1.0.7 -> 1.0.8.security (*)
+      |    |    |         +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.72 -> 1.8.0 (*)
+      |    |    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.3.72 -> 1.8.22 (*)
+      |    |    |         +--- android.arch.persistence:db:1.1.1 -> androidx.sqlite:sqlite:2.3.0 (*)
+      |    |    |         \--- android.arch.persistence:db-framework:1.1.1 -> androidx.sqlite:sqlite-framework:2.3.0 (*)
+      |    |    +--- com.heytap.nearx:httpdns:***********
+      |    |    |    +--- com.heytap.nearx:cloudconfig-area:2.3.9 -> ******* (*)
+      |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.4.0 -> 1.8.0 (*)
+      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.22 (*)
+      |    |    |    +--- com.heytap.nearx:exception-collector:1.0.12 -> 1.0.13 (*)
+      |    |    |    \--- com.heytap.nearx:database:1.0.22 -> 1.0.23 (*)
+      |    |    +--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.11.0 (*)
+      |    |    +--- com.heytap.nearx:taphttp-env-oversea:***********
+      |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.22 (*)
+      |    |    +--- com.oplus.nearx:track:3.4.16
+      |    |    |    +--- androidx.multidex:multidex:2.0.0
+      |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
+      |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.61 -> 1.8.0 (*)
+      |    |    |    +--- com.heytap.nearx:database:1.0.23 (*)
+      |    |    |    +--- com.heytap.nearx:utils:1.0.8.security (*)
+      |    |    |    +--- com.oplus.nearx:exception-collector:3.4.16
+      |    |    |    |    +--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
+      |    |    |    |    \--- com.oplus.nearx:visualize-assist:3.4.16
+      |    |    |    |         \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.61 -> 1.8.0 (*)
+      |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.6.0 (*)
+      |    |    |    \--- com.oplus.nearx:visualize-assist:3.4.16 (*)
+      |    |    +--- com.opos.ad:stobus-base:1.0.3 (*)
+      |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      |    +--- com.opos.ad:stobus-strategy:1.0.3 (*)
+      |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+      \--- com.opos.ad:overseas-ad-mix-ad-global-pub:********
+           +--- com.opos.ad:overseas-ad-cmn-base:******** (*)
+           +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
+           \--- com.opos.ad:overseas-ad-strategy-global:******** (*)
