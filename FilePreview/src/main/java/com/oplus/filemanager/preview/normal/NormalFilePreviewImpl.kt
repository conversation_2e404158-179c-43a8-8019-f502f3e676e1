/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - NormalFilePreviewImpl.kt
 * Description:
 *     The implementation to show file icon to preview it.
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-17   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.normal

import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.utils.getMediaType
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite

internal class NormalFilePreviewImpl(
    private val lifecycleOwner: LifecycleOwner,
    private val previewModel: FilePreviewViewModel
) : INormalFilePreview {

    private companion object {
        private const val TAG = "NormalFilePreviewImpl"
    }

    private val defIconPlaceholder =
        KtThumbnailHelper.getClassifyResId(MimeTypeHelper.UNKNOWN_TYPE)
    private val onFileLoaded = Observer<BaseFileBean?> { displayOnContainer(it) }

    private var isObserved = false
    private var infoContainer: PreviewFileInfoSuite? = null
    private var mDateTv: AppCompatTextView? = null
    private var mSizeTv: AppCompatTextView? = null
    private var nameContainer: TextViewSnippet? = null

    override fun release() {
        previewModel.releaseLoader(onFileLoaded)
        infoContainer = null
        isObserved = false
    }

    override fun setFileInfoStruct(view: View) {
        mDateTv = view.findViewById(R.id.preview_remote_time_info)
        mSizeTv = view.findViewById(R.id.preview_remote_size_info)
        nameContainer = view.findViewById(R.id.preview_remote_title)
    }

    override fun attachToContainer(container: PreviewFileInfoSuite) {
        infoContainer = container
        container.setFileIcon(defIconPlaceholder)
        if (isObserved) {
            return
        }
        isObserved = true
        previewModel.loadPreviewFile(lifecycleOwner, onFileLoaded)
    }

    private fun displayOnContainer(fileBean: BaseFileBean?) {
        val container = infoContainer ?: return
        container.setFileName(fileBean?.mDisplayName)
        container.setFileNameVisible(false)
        container.setFilePathVisible(false)
        val type = fileBean?.getMediaType(container.context)
        Log.d(TAG, "displayOnContainer: type=$type")
        if (type == null) {
            container.setFileIcon(defIconPlaceholder)
            return
        }
        val iconRes = KtThumbnailHelper.getClassifyResId(type)
        container.setFileIcon(iconRes)
        fileBean.let {
            nameContainer?.text = it.mDisplayName
            mDateTv?.text = Utils.getDateFormat(MyApplication.sAppContext, it.mDateModified)
            mSizeTv?.text = Utils.byteCountToDisplaySize(it.mSize)
        }
    }
}