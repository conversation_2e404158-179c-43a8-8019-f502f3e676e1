/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileSelectPathService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 11:39
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2022/7/21       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist

import android.content.Intent
import android.os.Messenger
import android.os.Handler
import android.os.Bundle
import android.os.Looper
import android.os.Message
import android.os.IBinder
import android.os.RemoteException
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Log
import com.oplus.fileservice.bean.FileListData
import com.oplus.fileservice.bean.FileResponseStatusCode
import com.oplus.fileservice.bean.ResponseData
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.filelist.scanner.SelectPathScanner
import com.filemanager.common.utils.HansFreezeManager
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebFileConstant.DEFAULT_PAGE_NO
import com.oplus.fileservice.utils.WebFileConstant.DEFAULT_PAGE_SIZE
import com.oplus.fileservice.utils.WebFileHelper

class FileSelectPathService : BaseService() {
    companion object {
        const val TAG = "FileSelectPathService"
        private const val LOAD_FILE_PATH = "filePath"
    }

    var mResult: WebScannerResult? = null

    override fun onCreate() {
        Log.d(TAG, "onCreate")
        super.onCreate()
    }

    override fun onBind(intent: Intent): IBinder? {
        Log.d(TAG, "onBind() getAction = ${intent.action} , getExtras =${intent.extras}")
        HansFreezeManager.instance.keepBackgroundRunning()
        val extras = intent.extras
        extras?.apply {
            mFilePath = getString(LOAD_FILE_PATH)
            mPageNo = getInt(PAGE_NO, DEFAULT_PAGE_NO)
            mPageSize = getInt(PAGE_SIZE, DEFAULT_PAGE_SIZE)
            mSortOrder = getInt(SORT_ORDER, SortHelper.FILE_NAME_ORDER)
        }
        Log.d(
            TAG,
            "mFilePath =$mFilePath, mPageNo = $mPageNo, mPageSize = $mPageSize, mSortOrder =$mSortOrder"
        )
        if (!WebFileHelper.isLegalPathString(mFilePath)) {
            mFilePath = mInternalPath
        }

        val selectPathScanner = SelectPathScanner(mFilePath!!, mSortOrder, mPageNo, mPageSize)
        mResult = selectPathScanner.scannerFiles()

        val bundle = Bundle()
        if (mResult == null) {
            val result = ResponseData(
                FileResponseStatusCode.LoaderTimeOut.code,
                "",
                FileResponseStatusCode.LoaderTimeOut.description
            )
            bundle.putString(WebFileConstant.DATA, mGson.toJson(result))
        } else {
            val result = ResponseData(
                FileResponseStatusCode.OK.code,
                FileListData(
                    mResult!!.webFileBeans,
                    mResult!!.pageNo,
                    mResult!!.total,
                    mFilePath!!
                ),
                FileResponseStatusCode.OK.description
            )
            bundle.putString(WebFileConstant.DATA, mGson.toJson(result))
        }
        return Messenger(Handler(
            Looper.getMainLooper()
        ) { msg: Message ->
            val replyMsg = Message.obtain()
            replyMsg.data = bundle
            try {
                msg.replyTo.send(replyMsg)
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
            true
        }).binder
    }

    override fun onUnbind(intent: Intent): Boolean {
        Log.d(TAG, "onUnbind")
        HansFreezeManager.instance.cancelFrozenDelay()
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        super.onDestroy()
    }
}