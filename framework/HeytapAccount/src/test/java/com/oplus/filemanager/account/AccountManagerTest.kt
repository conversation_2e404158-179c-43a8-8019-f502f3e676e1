/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : AccountManagerTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/9 10:50
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/9       1.0      create
 **********************************************************************/
package com.oplus.filemanager.account

import android.content.Context
import com.heytap.usercenter.accountsdk.AccountAgent
import com.heytap.usercenter.accountsdk.AccountAgentClient
import com.heytap.usercenter.accountsdk.http.AccountNameTask
import com.heytap.usercenter.accountsdk.model.AccountEntity
import com.heytap.usercenter.accountsdk.model.SignInAccount
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AccountManagerTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        MockKAnnotations.init()
        context = mockk(relaxed = true)
    }

    @After
    fun teardown() {
        unmockkAll()
    }

    @Test
    fun testInitSdk() {
        mockkStatic(AccountAgentClient::class)
        val client = mockk<AccountAgentClient>()
        every { AccountAgentClient.get() }.returns(client)
        justRun { client.init(any()) }
        AccountManager.initSdk(context)
        verify { client.init(any()) }
        unmockkStatic(AccountAgentClient::class)
    }

    @Test
    fun testIsLoginWhenAccountEntityIsNUll() {
        mockkStatic(AccountAgent::class)
        every { AccountAgent.getAccountEntity(context, "") }.returns(null)
        val isLogin = AccountManager.isLogin(context)
        Assert.assertFalse(isLogin)
        unmockkStatic(AccountAgent::class)
    }

    @Test
    fun testIsLoginWhenAuthTokenIsNull() {
        mockkStatic(AccountAgent::class)
        val accountEntity = AccountEntity()
        accountEntity.accountName = ACCOUNT_NAME
        accountEntity.authToken = null
        every { AccountAgent.getAccountEntity(context, "") }.returns(accountEntity)
        val isLogin = AccountManager.isLogin(context)
        Assert.assertFalse(isLogin)
        unmockkStatic(AccountAgent::class)
    }

    @Test
    fun testIsLoginWhenAuthTokenIsEmpty() {
        mockkStatic(AccountAgent::class)
        val accountEntity = AccountEntity()
        accountEntity.accountName = ACCOUNT_NAME
        accountEntity.authToken = ""
        every { AccountAgent.getAccountEntity(context, "") }.returns(accountEntity)
        val isLogin = AccountManager.isLogin(context)
        Assert.assertFalse(isLogin)
        unmockkStatic(AccountAgent::class)
    }

    @Test
    fun testIsLoginWhenAuthTokenIsValid() {
        mockkStatic(AccountAgent::class)
        val accountEntity = AccountEntity()
        accountEntity.accountName = ACCOUNT_NAME
        accountEntity.authToken = ACCOUNT_TOKEN
        every { AccountAgent.getAccountEntity(context, "") }.returns(accountEntity)
        val isLogin = AccountManager.isLogin(context)
        Assert.assertTrue(isLogin)
        unmockkStatic(AccountAgent::class)
    }

    @Test
    fun testLoginWhenAccountLogin() {
        mockkStatic(AccountAgent::class)
        val account = SignInAccount()
        account.isLogin = true
        val callback = slot<AccountNameTask.onReqAccountCallback<SignInAccount>>()
        every { AccountAgent.reqSignInAccount(context, "", capture(callback)) }.answers {
            callback.captured.onReqStart()
            callback.captured.onReqLoading()
            callback.captured.onReqFinish(account)
        }
        val call: (Boolean) -> Unit = spyk()
        AccountManager.login(context, true, call)
        verify { call.invoke(true) }
        unmockkStatic(AccountAgent::class)
    }

    @Test
    fun testLoginWhenAccountNotLogin() {
        mockkStatic(AccountAgent::class)
        val account = SignInAccount()
        account.isLogin = false
        val callback = slot<AccountNameTask.onReqAccountCallback<SignInAccount>>()
        every { AccountAgent.reqSignInAccount(context, "", capture(callback)) }.answers {
            callback.captured.onReqStart()
            callback.captured.onReqLoading()
            callback.captured.onReqFinish(account)
        }
        val call: (Boolean) -> Unit = spyk()
        AccountManager.login(context, true, call)
        verify { call.invoke(false) }
        unmockkStatic(AccountAgent::class)
    }

    @Test
    fun testLoginWhenAccountIsNull() {
        mockkStatic(AccountAgent::class)
        val callback = slot<AccountNameTask.onReqAccountCallback<SignInAccount>>()
        every { AccountAgent.reqSignInAccount(context, "", capture(callback)) }.answers {
            callback.captured.onReqStart()
            callback.captured.onReqLoading()
            callback.captured.onReqFinish(null)
        }
        val call: (Boolean) -> Unit = spyk()
        AccountManager.login(context, true, call)
        verify { call.invoke(false) }
        unmockkStatic(AccountAgent::class)
    }

    @Test
    fun testGetUserToken() {
        mockkStatic(AccountAgent::class)
        val accountEntity = AccountEntity()
        accountEntity.accountName = ACCOUNT_NAME
        accountEntity.authToken = ACCOUNT_TOKEN
        every { AccountAgent.getAccountEntity(context, "") }.returns(accountEntity)
        val token = AccountManager.getUserToken(context)
        Assert.assertEquals(ACCOUNT_TOKEN, token)
        unmockkStatic(AccountAgent::class)
    }

    @Test
    fun testGetUserTokenWhenAuthTokenNull() {
        mockkStatic(AccountAgent::class)
        val accountEntity = AccountEntity()
        accountEntity.accountName = ACCOUNT_NAME
        accountEntity.authToken = null
        every { AccountAgent.getAccountEntity(context, "") }.returns(accountEntity)
        val token = AccountManager.getUserToken(context)
        Assert.assertEquals("", token)
        unmockkStatic(AccountAgent::class)
    }

    @Test
    fun testGetUserTokenWhenAccountEntityIsNull() {
        mockkStatic(AccountAgent::class)
        every { AccountAgent.getAccountEntity(context, "") }.returns(null)
        val token = AccountManager.getUserToken(context)
        Assert.assertEquals("", token)
        unmockkStatic(AccountAgent::class)
    }

    companion object {
        private const val ACCOUNT_NAME = "ternence"
        private const val ACCOUNT_TOKEN = "123456"
    }
}