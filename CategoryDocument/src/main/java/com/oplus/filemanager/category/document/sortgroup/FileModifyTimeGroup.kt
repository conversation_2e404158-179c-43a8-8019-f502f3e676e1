/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/14
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.oplus.filemanager.category.document.sortgroup

import com.filemanager.common.MyApplication
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.category.document.R
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.temporal.JulianFields
import java.util.Locale

class FileModifyTimeGroup : AbsFileGroup() {

    companion object {
        const val TODAY = 0
        const val DAY_OFFSET_1 = 1
        const val DAY_OFFSET_2 = 2
        const val DAY_OFFSET_3 = 3
    }

    private val dataFormat = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())

    override fun createGroupTitle(fileWrapper: MediaFileWrapper): String {
        val nowDay = LocalDateTime.now().getLong(JulianFields.JULIAN_DAY).toInt()
        val fileOpenTimeDay = LocalDateTime.ofInstant(Instant.ofEpochMilli(fileWrapper.mDateModified), ZoneId.systemDefault())
            .getLong(JulianFields.JULIAN_DAY).toInt()
        return when (val dayOffset = nowDay - fileOpenTimeDay) {
            TODAY -> MyApplication.sAppContext.resources.getString(com.filemanager.common.R.string.document_sort_string_today)

            DAY_OFFSET_1, DAY_OFFSET_2, DAY_OFFSET_3 -> {
                MyApplication.sAppContext.resources.getQuantityString(
                    com.filemanager.common.R.plurals.document_sort_string_x_days_ago,
                    dayOffset,
                    dayOffset
                )
            }

            else -> dataFormat.format(fileWrapper.mDateModified)
        }
    }
}