/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardItemData
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/13 19:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.data

import androidx.annotation.Keep

@Keep
data class RecentCardItemData(
    val title: String,
    val date: String,
    val img: String,
    val filePath: String,
    val fileName: String,
    val fileExt: String,
    val imageType: String,
    val hasData: Boolean
) {
    override fun toString(): String {
        return "RecentCardItemData(title='$title', date='$date', img='$img', filePath='$filePath')"
    }
}