package com.oplus.filemanager.interfaze.remotedevice

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.GsonUtil
import com.google.gson.Gson
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.json.JSONObject
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * DownloadTransferCard的单元测试类
 * 用于测试DownloadTransferCard类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决错误
class DownloadTransferCardTest {

    // 测试用的DownloadTransferCard实例
    private lateinit var testCard: TestDownloadTransferCard

    /**
     * 测试用的DownloadTransferCard实现类
     * 用于模拟实际的DownloadTransferCard行为
     */
    private class TestDownloadTransferCard(
        status: Int,
        progress: Int,
        files: List<String>,
        appIcon: String = "@drawable/ic_launcher_filemanager",
        appName: String = "TestApp",
        fileIcon: String = ""
    ) : DownloadTransferCard(status, progress, files, appIcon, appName, fileIcon) {
        /**
         * 实现抽象方法，返回测试用的页面ID
         */
        override fun pageId(): String = "test_page"
    }

    /**
     * 测试前的准备工作
     * 1. 重置静态变量
     * 2. 模拟MyApplication
     * 3. 模拟GsonUtil
     */
    @Before
    fun setUp() {
        // 重置静态变量
        DownloadTransferCard.showFluidCloud = false
        
        // 模拟MyApplication
        val mockContext = mockk<Context>(relaxed = true)
        mockkObject(MyApplication)
        every { MyApplication.appContext } returns mockContext
        every { mockContext.getString(any()) } returns "TestApp"

        // 模拟GsonUtil
        mockkObject(GsonUtil)
        every { GsonUtil.toJson(any<DownloadTransferCard>()) } answers {
            val card = firstArg<DownloadTransferCard>()
            "{\"status\":${card.status},\"progress\":${card.progress}}"
        }
    }

    /**
     * 测试后的清理工作
     * 解除所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试toJSON方法
     * 验证是否能正确将对象转换为JSON格式
     */
    @Test
    fun testToJSON() {
        testCard = TestDownloadTransferCard(
            status = DownloadTransferCard.STATUS_DOWNLOADING,
            progress = 50,
            files = listOf("file1.txt", "file2.txt")
        )

        val json = testCard.toJSON()
        assertNotNull(json)
        assertEquals(DownloadTransferCard.STATUS_DOWNLOADING, json.getInt("status"))
        assertEquals(50, json.getInt("progress"))
    }

    /**
     * 测试isMilestone方法
     * 验证是否能正确判断是否为里程碑状态
     * 测试场景包括：
     * 1. 下载中0%进度
     * 2. 下载中100%进度
     * 3. 下载中50%进度
     * 4. 失败状态
     * 5. 等待状态
     * 6. 默认状态
     */
    @Test
    fun testIsMilestone() {
        // 测试下载中0%进度
        testCard = TestDownloadTransferCard(
            status = DownloadTransferCard.STATUS_DOWNLOADING,
            progress = 0,
            files = emptyList()
        )
        assertTrue(testCard.isMilestone())

        // 测试下载中100%进度
        testCard = TestDownloadTransferCard(
            status = DownloadTransferCard.STATUS_DOWNLOADING,
            progress = DownloadTransferCard.PROGRESS_MAX,
            files = emptyList()
        )
        assertTrue(testCard.isMilestone())

        // 测试下载中50%进度
        testCard = TestDownloadTransferCard(
            status = DownloadTransferCard.STATUS_DOWNLOADING,
            progress = 50,
            files = emptyList()
        )
        assertFalse(testCard.isMilestone())

        // 测试失败状态
        testCard = TestDownloadTransferCard(
            status = DownloadTransferCard.STATUS_FAIL,
            progress = 0,
            files = emptyList()
        )
        assertTrue(testCard.isMilestone())

        // 测试等待状态
        testCard = TestDownloadTransferCard(
            status = DownloadTransferCard.STATUS_WAIT,
            progress = 0,
            files = emptyList()
        )
        assertTrue(testCard.isMilestone())

        // 测试默认状态
        testCard = TestDownloadTransferCard(
            status = DownloadTransferCard.STATUS_DEFAULT,
            progress = 0,
            files = emptyList()
        )
        assertFalse(testCard.isMilestone())
    }

    /**
     * 测试伴生对象中的常量值
     * 验证状态常量和进度最大值是否正确
     */
    @Test
    fun testCompanionObjectValues() {
        assertEquals(0, DownloadTransferCard.STATUS_DEFAULT)
        assertEquals(1, DownloadTransferCard.STATUS_DOWNLOADING)
        assertEquals(2, DownloadTransferCard.STATUS_WAIT)
        assertEquals(3, DownloadTransferCard.STATUS_FAIL)
        assertEquals(100, DownloadTransferCard.PROGRESS_MAX)
    }

    /**
     * 测试pageId方法
     * 验证是否能正确返回页面ID
     */
    @Test
    fun testPageId() {
        testCard = TestDownloadTransferCard(
            status = DownloadTransferCard.STATUS_DOWNLOADING,
            progress = 50,
            files = listOf("file1.txt")
        )
        assertEquals("test_page", testCard.pageId())
    }

    /**
     * 测试静态变量隔离性
     * 验证静态变量修改不会影响其他测试
     */
    @Test
    fun testStaticVariableIsolation() {
        // 确保静态变量不会影响其他测试
        DownloadTransferCard.showFluidCloud = true
        assertEquals(true, DownloadTransferCard.showFluidCloud)
    }
}