/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - RecycleOperationListener.java
 ** Description: Recycle Operation Listener
 ** Version: 1.0
 ** Date : 2020/03/10
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/03/10    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.operation.listener

import android.app.Activity
import android.content.DialogInterface
import android.os.Message
import android.view.Gravity
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AlertDialog
import com.filemanager.common.wrapper.RecycleBinLiveData
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.helper.getBottomAlertDialogWindowGravity
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.recyclebin.utils.RecycleBinUtils
import com.filemanager.recyclebin.operation.BaseOperation
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_ERROR_CANCEL
import com.filemanager.recyclebin.operation.BaseOperation.OperationParameters
import com.filemanager.recyclebin.operation.BaseOperation.OperationResult
import com.filemanager.recyclebin.operation.RecycleBinOperationListener
import com.filemanager.recyclebin.operation.RecycleParameters
import java.lang.ref.WeakReference

class RecycleOperationListener(
    activity: ComponentActivity,
    var mListener: RecycleBinOperationListener?,
    private val mIsSelectAll: Boolean = false,
    private var showProgress: Boolean = true
) : OperationListener(activity) {
    companion object {
        private const val TAG = "RecycleOperationListener"

        private fun createProgressDialog(activity: Activity): AlertDialog? {
            if (activity.isFinishing || activity.isDestroyed) {
                Log.w(TAG, "createProgressDialog ACTIVITY INVALID")
                return null
            }
            val dialog = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Progress)
                .setTitle(com.filemanager.common.R.string.dialog_deleting)
                .setCancelable(false).create()
            dialog.setCanceledOnTouchOutside(false)
            return dialog
        }
    }

    private var mProgressDialog: AlertDialog? = null

    override fun innerHandleMessage(message: Message, activity: ComponentActivity) {
        Log.d(TAG, "handleMessage what : ${message.what}")
        mWeakActivity.get() ?: return
        when (message.what) {
            MSG_SHOW_PROGRESS_DIALOG -> {
                mProgressDialog?.show()
            }

            MSG_UPDATE_PROGRESS_DIALOG -> {
                if (message.obj is Int) {
                    mProgressDialog?.window?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.progress =
                        (message.obj as Int)
                }
            }
        }
    }

    override fun onProgressStart() {
        Log.d(TAG, "onProgressStart showProgress: $showProgress")
        if (!showProgress) {
            return
        }
        mWeakActivity.get()?.let {
            mProgressDialog?.let {
                if (it.isShowing) {
                    it.dismiss()
                }
                mProgressDialog = null
            }
            mProgressDialog = createProgressDialog(it)
            mMainHandler.sendEmptyMessageDelayed(MSG_SHOW_PROGRESS_DIALOG, MESSAGE_DELAY_TIME)
        }
    }

    override fun onProgressUpdate(progress: Int?) {
        Log.d(TAG, "onProgressUpdate ${progress} ")
        progress?.let {
            mProgressDialog?.let {
                mMainHandler.removeMessages(MSG_UPDATE_PROGRESS_DIALOG)
                if (it.isShowing) {
                    it.window?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.progress = progress
                } else {
                    mMainHandler.sendMessageDelayed(mMainHandler.obtainMessage(MSG_UPDATE_PROGRESS_DIALOG, progress),
                            MESSAGE_DELAY_TIME)
                }
            }
        }
    }

    override fun onProgressComplete(parameters: OperationParameters?, result: OperationResult) {
        Log.d(TAG, "onProgressComplete result: $result")
        mMainHandler.removeCallbacksAndMessages(null)
        try {
            mProgressDialog?.let { dialog ->
                dialog.dismiss()
            }
        } catch (e: Exception) {
        }
        mProgressDialog = null

        mListener?.let {
            it.onOperationCompleted(RecycleBinLiveData.RECYCLE_RECYCLE, result)
        }

        // show delete comfirm dialog if recycler failed
        if (result.mStatusCode != BaseOperation.STATUS_SUCCESS) {
            mWeakActivity.get()?.let {
                when (result.mStatusCode) {
                    BaseOperation.STATUS_ERROR -> createDeleteForverDialog(it, parameters, mListener, result)
                    BaseOperation.STATUS_NOT_ENOUGH_SPACE -> {
                        if (KtAppUtils.hasCleanupFunction) {
                            buildNotEnoughStorageSpaceDialog(it, result, parameters)
                        } else {
                            createDeleteForverDialog(it, parameters, mListener, result)
                        }
                    }
                    else -> {}
                }
            }
        }
    }

    override fun recycle() {
        mListener = null
        try {
            mProgressDialog?.dismiss()
        } catch (e: Exception) {
        }
        mProgressDialog = null
        super.recycle()
    }

    private fun buildNotEnoughStorageSpaceDialog(
        activity: ComponentActivity,
        result: OperationResult,
        parameters: OperationParameters?
    ) {
        val isMediaType = if (parameters is RecycleParameters) {
            parameters.mIsMediaType
        } else {
            false
        }
        val builder = COUIAlertDialogBuilder(
            activity,
            com.support.dialog.R.style.COUIAlertDialog_Bottom
        )
            .setWindowGravity(Gravity.BOTTOM)
            .setTitle(
                RecycleBinUtils.getDeleteDialogTitle(
                    result.mTotalCount,
                    mIsSelectAll
                )
            )
            .setMessage(
                RecycleBinUtils.getForeverDeleteMessage(
                    result.mTotalCount,
                    mIsSelectAll
                )
            )
            .setPositiveButton(com.filemanager.common.R.string.clean_memory) { _, _ ->
                KtAppUtils.startPhoneManager(activity)
            }
            .setNeutralButton(com.filemanager.common.R.string.menu_recycle_delete_forever) { _, _ ->
                RecycleBinUtils.innerDelete(activity, parameters, mListener, isMediaType)
            }
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, null)
        builder.show()
    }

    private fun createDeleteForverDialog(activity: ComponentActivity, parameters: OperationParameters?,
                                         listener: RecycleBinOperationListener?,
                                         result: OperationResult)
            : AlertDialog {
        val confirmListener = RecycleConfirmDialogListener(activity, parameters, listener, result)

        return COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom)
                .setWindowGravity(getBottomAlertDialogWindowGravity(activity))
                .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(activity))
                .setMessage(com.filemanager.common.R.string.toast_recycle_file_error_tip)
                .setOnCancelListener(confirmListener)
                .setNeutralButton(
                    RecycleBinUtils.getDelButtonStr((mIsSelectAll && (result.mFailedCount == result.mTotalCount))),
                        confirmListener)
                .setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, confirmListener).show()
    }

    private class RecycleConfirmDialogListener(activity: ComponentActivity, val mParameters: OperationParameters?,
                                               val mListener: RecycleBinOperationListener?,
                                               val mRecycleResult: OperationResult)
        : DialogInterface.OnClickListener, DialogInterface.OnCancelListener {

        private val mWeakActivity = WeakReference(activity)

        override fun onClick(dialog: DialogInterface?, which: Int) {
            Log.d(TAG, "onClick which: $which")
            when (which) {
                DialogInterface.BUTTON_NEUTRAL -> {
                    mWeakActivity.get()?.let {
                        val isMediaType = if (mParameters is RecycleParameters) {
                            mParameters.mIsMediaType
                        } else {
                            false
                        }

                        RecycleBinUtils.innerDelete(it, mParameters, mListener, isMediaType)
                    }
                }
                else -> {
                    mListener?.let {
                        mListener.onOperationCompleted(
                            RecycleBinLiveData.RECYCLE_RECYCLE,
                                OperationResult(mRecycleResult.mTotalCount, mRecycleResult.mFailedCount, STATUS_ERROR_CANCEL))
                    }
                }
            }
            dialog?.dismiss()
        }

        override fun onCancel(dialog: DialogInterface?) {
            dialog?.dismiss()
            mListener?.let {
                mListener.onOperationCompleted(
                    RecycleBinLiveData.RECYCLE_RECYCLE,
                        OperationResult(mRecycleResult.mTotalCount, mRecycleResult.mFailedCount, STATUS_ERROR_CANCEL))
            }
        }
    }
}