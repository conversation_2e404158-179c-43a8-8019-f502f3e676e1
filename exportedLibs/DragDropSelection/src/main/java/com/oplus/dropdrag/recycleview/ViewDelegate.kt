/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - ViewDelegate.kt
 ** Description: ViewDelegate
 ** Version: 1.0
 ** Date : 2020/05/15
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/15    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.recycleview

import android.view.MotionEvent
import android.view.View
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.utils.Log
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.SelectionTracker.Companion.DEBUG
import com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE
import com.oplus.dropdrag.base.SelectionViewHolder

internal abstract class ViewDelegate {
    abstract val height: Int

    abstract fun getItemUnder(e: MotionEvent): Int

    abstract fun getLastGlidedItemPosition(e: MotionEvent): Int

    abstract fun viewCanScrollUp(): Boolean
}

internal class RecyclerViewDelegate(private val mRecyclerView: RecyclerView,
                                    private val mLayoutType: LAYOUT_TYPE = LAYOUT_TYPE.LIST) : ViewDelegate() {

    companion object {
        private const val TAG = "RecyclerViewDelegate"
        private const val NO_SCROLL_UP = -1

        /*
         * Check to see if MotionEvent if past a particular item, i.e. to the right or to the bottom
         * of the item. For RTL, it would to be to the left or to the bottom of the item.
         */
        private fun isPastLastItem(
                top: Int, left: Int, right: Int, e: MotionEvent, direction: Int): Boolean {
            // make sure can select last line all item if e.y > top
            return if (direction == View.LAYOUT_DIRECTION_LTR) {
                ((e.x > right) && (e.y > top))
            } else {
                ((e.x < left) && (e.y > top))
            }
        }

        // It's possible for events to go over the top/bottom of the RecyclerView.
        // We want to get a Y-coordinate within the RecyclerView so we can find the childView underneath
        // correctly.
        private fun getInboundY(max: Float, y: Float): Float {
            if (y < 0f) {
                return 0f
            } else if (y > max) {
                return max
            }
            return y
        }
    }

    override fun viewCanScrollUp(): Boolean {
        return mRecyclerView.canScrollVertically(NO_SCROLL_UP) ?: false
    }

    override val height: Int
        get() = mRecyclerView.height

    override fun getItemUnder(e: MotionEvent): Int {
        val child = mRecyclerView.findChildViewUnder(e.x, e.y)
        return if (child != null) {
            mRecyclerView.getChildAdapterPosition(child)
        } else {
            RecyclerView.NO_POSITION
        }
    }

    override fun getLastGlidedItemPosition(e: MotionEvent): Int {
        // NOTE: Stop scroll if slide over recyclerview.such as in zoom window
        if (e.y > (mRecyclerView.bottom)) {
            return RecyclerView.NO_POSITION
        }

        // If user has moved his pointer to the bottom-right empty pane (ie. to the right of the
        // last item of the recycler view), we would want to set that as the currentItemPos
        val layoutManager = mRecyclerView.layoutManager ?: return RecyclerView.NO_POSITION
        if (layoutManager.childCount <= 0) {
            return RecyclerView.NO_POSITION
        }

        /**
         * layoutManager.childCount: the current number of child views attached to the parent RecyclerView.
         * This does not include child views that were temporarily detached and/or scrapped.
         */
        val lastItem = layoutManager.getChildAt(layoutManager.childCount - 1)
                ?: return RecyclerView.NO_POSITION

        val direction = ViewCompat.getLayoutDirection(mRecyclerView)
        val pastLastItem = isPastLastItem(lastItem.top, lastItem.left, lastItem.right, e, direction)

        if (DEBUG) {
            Log.d(TAG, "getLastGlidedItemPosition pastLastItem: $pastLastItem ,childCount: ${layoutManager.childCount}")
        }

        if (pastLastItem) {
            val itemCount = mRecyclerView.adapter?.itemCount ?: return RecyclerView.NO_POSITION
            if (DEBUG) {
                Log.d(TAG, "getLastGlidedItemPosition itemCount: $itemCount")
            }

            if (itemCount <= 0) {
                return RecyclerView.NO_POSITION
            } else {
                return itemCount - 1
            }
        } else {
            // find child view where in user point
            var inboundY: Float = getInboundY(mRecyclerView.bottom.toFloat(), e.y)
            var view = mRecyclerView.findChildViewUnder(e.x, inboundY)

            // check if or not pass the last item, if true, return index of last item
            if (view == null) {
                inboundY = getInboundY(lastItem.bottom.toFloat(), e.y)
                view = mRecyclerView.findChildViewUnder(e.x, inboundY)
            }

            view?.let {
                val itemHolder = mRecyclerView.findContainingViewHolder(it)
                if (itemHolder is SelectionViewHolder<*>) {
                    val itemDetail = itemHolder.getItemDetails() ?: return RecyclerView.NO_POSITION
                    // check if or not in selection hotspot
                    if (itemDetail.inSelectionHotspot(e)) {
                        return mRecyclerView.getChildAdapterPosition(it)
                    } else {
                        // add for label item in recycleradapter to skip check
                        val key = itemDetail.selectionKey ?: return RecyclerView.NO_POSITION
                        if (SelectionTracker.isSkipKey(key)) {
                            return mRecyclerView.getChildAdapterPosition(it)
                        } else if (e.y > inboundY) {
                            Log.d(TAG, "getLastGlidedItemPosition over border")
                            // over border of grid layout or last item in visible region
                            if (mLayoutType == LAYOUT_TYPE.GRID) {
                                return mRecyclerView.getChildAdapterPosition(it)
                            } else if (lastItem.bottom <= mRecyclerView.bottom) {
                                // over boder of list layout and last item in visible region
                                return mRecyclerView.getChildAdapterPosition(it)
                            }
                        }
                    }
                }
                Log.d(TAG, "getLastGlidedItemPosition NOT IN SELECTION REGION")
            }
        }
        return RecyclerView.NO_POSITION
    }
}


