/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/DocumentLoader
 * * Description:
 * * Version:1.0
 * * Date :2020/6/5
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/6/5,        v1.0,           Create
 ****************************************************************/
package com.oplus.filemanager.category.document.ui

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.loader.BaseUriLoader
import com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.DOC_TYPE_OFD
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.DOC_TYPE_PDF
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.DOC_TYPE_TXT
import com.oplus.filemanager.category.document.utils.DocumentSortHelper
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.interfaze.main.IMain
import java.util.*
import kotlin.collections.ArrayList

class DocumentLoader @Suppress("LongParameterList") constructor(
    context: Context,
    uri: Uri?,
    sql: String?,
    tabPosition: Int,
    extension: String?,
    docArray: ArrayList<String>?,
    lastOpenTimeOrder: Boolean
) : BaseUriLoader<Int, MediaFileWrapper>(context) {
    companion object {
        private const val TAG = "DocumentLoader"
        private const val INDEX_ID = 0
        private const val INDEX_DATA = 1
        private const val INDEX_DISPLAY_NAME = 2
        private const val INDEX_SIZE = 3
        private const val INDEX_DATE_MODIFIED = 4
        private const val INDEX_DATE_MIME_TYPE = 5
        private val MEDIA_PROJECT = arrayOf(
                MediaStore.Files.FileColumns._ID,
                MediaStore.Files.FileColumns.DATA,
                MediaStore.Files.FileColumns.DISPLAY_NAME,
                MediaStore.Files.FileColumns.SIZE,
                MediaStore.Files.FileColumns.DATE_MODIFIED,
                MediaStore.Files.FileColumns.MIME_TYPE
        )


        fun getAllSelectionArgs(fileExts: ArrayList<String>?): ArrayList<String?> {
            val selectionArgs = ArrayList<String?>()
            if (fileExts.isNullOrEmpty()) {
                selectionArgs.addAll(DocumentViewModel.DEF_ALL_EXT_ARRAY)
            } else {
                for (fileExt in fileExts) {
                    selectionArgs.add(fileExt)
                    if (!DOC_TYPE_PDF.equals(fileExt, true)
                        && !DOC_TYPE_OFD.equals(fileExt, true)
                        && !DOC_TYPE_TXT.equals(fileExt, true)) {
                        selectionArgs.add("${fileExt}x")
                    }
                }
            }
            return selectionArgs
        }
    }

    /**
     * If the settings--storage-space call open the page,
     * the flag [mIsNeedFilter] will be set to false and only the files in the phone storage will be displayed.
     * Otherwise, the flag will be set to true, which is called internally by the File-Manager,
     * and need to check whether the file needs to be filtered
     */
    private var mIsNeedFilter: Boolean = Utils.getFilterState()
    private var mTabPosition: Int = tabPosition
    private var mSql: String? = sql
    private var mInternalPath: String? = VolumeEnvironment.getInternalSdPath(appContext)
    private var mSort: Int = SortModeUtils.getSharedSortMode(
        appContext,
        SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC)
    )
    private var mExtension: String? = extension
    private var mDocArray: ArrayList<String>? = docArray
    private var mLowerCaseSearchKey: String? = null
    private var mBaseQuerySelection: String = ""
    private var mFilter = DocumentFilter()
    private var mIsInit = true
    /** 打开时间，当从侧边栏打开页面时，固定显示打开时间，选择其他排序后，不再使用 */
    private var isLastOpenTimeOrder = lastOpenTimeOrder

    init {
        mUri = uri
        super.initData()
        initSelection()
    }

    private fun initSelection() {
        val sql = StringBuilder()
        val documentExtensionType = Injector.injectFactory<IDocumentExtensionType>()
        when (mTabPosition) {
            //All Tab
            0 -> {
                if (TextUtils.isEmpty(mSql)) {
                    val sqlString = documentExtensionType?.getDocumentSqlQuery(
                        documentExtensionType.getAllSelectionArgs(mDocArray)
                    )
                    sql.append(sqlString)
                } else {
                    sql.append(mSql)
                }
            }
            //Other Tab
            else -> {
                mExtension?.let {
                    val fileExtensions = ArrayList<String>()
                    fileExtensions.add(".${it.lowercase(Locale.getDefault())}")
                    val sqlString = documentExtensionType?.getDocumentSqlQuery(
                        documentExtensionType.getAllSelectionArgs(fileExtensions)
                    )
                    sql.append(sqlString)
                }
            }
        }
        if (!mIsNeedFilter) {
            if (!TextUtils.isEmpty(sql)) {
                sql.append(" AND ")
            }
            if (!SdkUtils.isAtLeastR()) {
                /**Confirmed with Media colleagues (XiongBobo W9002523)
                 * that both VOLUME_NAME [MediaStore.VOLUME_EXTERNAL_PRIMARY] [MediaStore.VOLUME_EXTERNAL] are stored on the phone,
                 * so they are added.*/
                sql.append(" ( ")
                sql.append(MediaStore.Files.FileColumns.VOLUME_NAME + " = '" + MediaStore.VOLUME_EXTERNAL_PRIMARY + "'")
                sql.append(" or ")
                sql.append(MediaStore.Files.FileColumns.VOLUME_NAME + " = '" + MediaStore.VOLUME_EXTERNAL + "'")
                sql.append(" ) ")
            } else {
                sql.append(MediaStore.Files.FileColumns.DATA + " LIKE '%" + mInternalPath + "%'")
            }
        }
        mBaseQuerySelection = sql.toString()
    }


    fun setSort(sort: Int) {
        //重新设置了排序，不再使用固定打开时间
        isLastOpenTimeOrder = false
        mSort = if (sort < 0) {
            SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
        } else {
            sort
        }
        mSortOrder = getSortOrder()
    }

    override fun getUri(): Uri? {
        mUri?.let {
            MediaHelper.FILE_URI
        }
        return mUri
    }

    override fun getSelection(): String {
        //文档分类列表下剔除文件夹类型，防止后缀名为.ppt之类的文件夹被读取出来
        return "$mBaseQuerySelection AND (${MediaStore.Files.FileColumns.MIME_TYPE} <> ${MimeTypeHelper.DIRECTORY_TYPE})"
    }

    override fun getSelectionArgs(): Array<String>? {
        return null
    }

    override fun getObserverUri(): Array<Uri>? {
        Log.w("Not yet implemented")
        return null
    }

    override fun getProjection(): Array<String> {
        return MEDIA_PROJECT
    }

    override fun preHandleBeforeBackground() {
        super.preHandleBeforeBackground()
        if (mIsInit) {
            mFilter.updateFilterOptions(mSort)
            mIsNeedFilter = Utils.getFilterState()
            mIsInit = false
        }
    }

    override fun createFromCursor(cursor: Cursor, uri: Uri?): MediaFileWrapper? {
        val id = cursor.getInt(INDEX_ID)
        val data = cursor.getString(INDEX_DATA)
        val displayName = cursor.getString(INDEX_DISPLAY_NAME)
        val size = cursor.getLong(INDEX_SIZE)
        var dateModified = cursor.getLong(INDEX_DATE_MODIFIED) * SECONDS_TO_MILLISECONDS
        val mimeType = cursor.getString(INDEX_DATE_MIME_TYPE)
        if (dateModified == 0L) {
            Log.d(TAG, "dateModified is 0")
            dateModified = FileTimeUtil.getFileTime(data) ?: 0
        }
        val mediaFileWrapper = MediaFileWrapper(id, data, displayName, mimeType, size, dateModified, MediaHelper.FILE_URI)
        if (TextUtils.isEmpty(mediaFileWrapper.mData) || TextUtils.isEmpty(mediaFileWrapper.mDisplayName)) {
            Log.d(TAG, "createFromCursor file is empty")
            return null
        }
        //file interface is slowly on Android R,So need not to call exists on Android R
        if (!SdkUtils.isAtLeastR() && !JavaFileHelper.exists(mediaFileWrapper)) {
            Log.d(TAG, "createFromCursor file not exists or isDirectory")
            return null
        }
        if (mIsNeedFilter) {
            if (mFilter.checkIsFilterItem(mediaFileWrapper)) {
                Log.d(TAG, "createFromCursor filter file")
                return null
            }
        }
        if (mLowerCaseSearchKey.isNullOrEmpty() || mediaFileWrapper.mDisplayName!!.lowercase(Locale.getDefault())
                .contains(mLowerCaseSearchKey!!.lowercase(Locale.getDefault()))) {
            return mediaFileWrapper
        }
        return null
    }

    override fun preHandleResultBackground(list: MutableList<MediaFileWrapper>): MutableList<MediaFileWrapper> {
        //从最近打开时间数据库中读取最近打开时间
        val fileOpenTimeAction = Injector.injectFactory<IFileOpenTime>()
        fileOpenTimeAction?.findLastOpenTime(list)
        Log.d(TAG, "preHandleResultBackground isLastOpenTimeOrder = $isLastOpenTimeOrder")
        if (isLastOpenTimeOrder) {
            //固定显示打开时间，降序
            DocumentSortHelper.sortFiles(list, SortHelper.FILE_LAST_OPEN_TIME_ORDER, true)
        } else {
            val sort = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
            val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
            DocumentSortHelper.sortFiles(list, sort, isDesc)
        }
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.findFileLabelIfHad(list)
        return list
    }

    override fun getItemKey(item: MediaFileWrapper): Int? = item.id
}