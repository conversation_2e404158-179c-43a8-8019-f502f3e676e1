/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:FileActionShare.kt
 * * Description:the model to achieve file_share method
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:liuzeming
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * liuzeming    20200109   1.0       the data class for transferring message to models
 ****************************************************************/
package com.filemanager.fileoperate.share

import android.content.Context
import android.graphics.Rect
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.base.BaseFileBean
import com.filemanager.fileoperate.base.BaseFileAction
import java.lang.ref.WeakReference

class FileActionShare(fileActionShareBean: FileActionShareBean) : BaseFileAction<FileShareObserver>(fileActionShareBean.lifecycle) {
    companion object {
        const val SEND_ENTRANCE: String = "send_entrance"
        private const val TAG = "FileActionShare"
    }

    private val mOperateFile = ArrayList<BaseFileBean>()
    private var mWeakContext = WeakReference(fileActionShareBean.context)
    private val mRect: Rect?

    init {
        mOperateFile.addAll(fileActionShareBean.files)
        mRect = fileActionShareBean.rect
    }

    override fun run(): Boolean {
        SendTask(mWeakContext, this, false).send(mOperateFile, mRect)
        mWeakContext.clear()
        return true
    }

    override fun onCancelled() {
        mWeakContext.clear()
        super.onCancelled()
    }

    data class FileActionShareBean(
        val lifecycle: LifecycleOwner,
        val files: List<out BaseFileBean>,
        val context: Context,
        val rect: Rect?
    )
}