<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.filemanager.common.view.PrivacyPolicyContentScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/privacy_tip_vertical_space"
        android:scrollbars="vertical"
        app:max_height="@dimen/privacy_policy_text_max_height">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/privacy_policy_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="@dimen/privacy_policy_text_line_space"
                android:paddingLeft="@dimen/privacy_content_horizontal_space"
                android:paddingRight="@dimen/privacy_content_horizontal_space"
                android:textColor="@color/black"
                android:textSize="@dimen/privacy_content_text_size" />

            <TextView
                android:id="@+id/privacy_link"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/privacy_tip_vertical_space"
                android:lineSpacingExtra="@dimen/privacy_policy_text_line_space"
                android:paddingLeft="@dimen/privacy_content_horizontal_space"
                android:paddingRight="@dimen/privacy_content_horizontal_space"
                android:textColor="@color/black_50_percent"
                android:textSize="@dimen/privacy_tip_text_size" />
        </LinearLayout>
    </com.filemanager.common.view.PrivacyPolicyContentScrollView>
</LinearLayout>
