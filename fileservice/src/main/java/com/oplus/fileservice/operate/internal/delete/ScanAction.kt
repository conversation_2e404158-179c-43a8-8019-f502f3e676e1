/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ScanAction
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 21:37
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/21       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import android.provider.MediaStore
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.utils.Utils

class ScanAction(
    bulkCount: Int = BATCH_SCAN_COUNT
) : InnerOperation(MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL), bulkCount) {

    override fun flush() {
        val size = mPending.size
        if (size > 0) {
            MediaScannerCompat.sendMultiDirMediaScanner(mPending, Utils.MEDIA_SCAN_RECYCLE_DELETE)
            mPending.clear()
        }
    }

    companion object {
        private const val BATCH_SCAN_COUNT = 50
    }
}