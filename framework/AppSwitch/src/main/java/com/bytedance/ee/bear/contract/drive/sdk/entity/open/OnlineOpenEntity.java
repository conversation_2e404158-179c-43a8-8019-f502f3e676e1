/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : OnlineOpenEntity.java
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * *
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.contract.drive.sdk.entity.open;

import android.os.Parcel;

public class OnlineOpenEntity extends BaseOpenEntity {

    //反编译拷贝过来
    /*private String authExtra;
    private boolean isEncryptedFile; //默认为true
    private String mountPoint = "";
    private int noPermissionTipsStringRes;
    private boolean restrictDownloadUsage;*/

    public static final Creator<OnlineOpenEntity> CREATOR = new Creator<OnlineOpenEntity>() {
        @Override
        public OnlineOpenEntity createFromParcel(Parcel in) {
            return new OnlineOpenEntity(in);
        }

        @Override
        public OnlineOpenEntity[] newArray(int size) {
            return new OnlineOpenEntity[size];
        }
    };

    protected OnlineOpenEntity(Parcel parcel) {
        super(parcel);
        parcel.readString();
        parcel.readByte(); //反编译会byte读取到没有，会转换为true或false赋值给restrictDownloadUsage
        parcel.readInt();
        parcel.readString();
        parcel.readInt();//反编译会int读取到没有，int==0会转换为false，赋值给isEncryptedFile
    }
}
