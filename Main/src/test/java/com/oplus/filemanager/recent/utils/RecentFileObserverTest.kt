package com.oplus.filemanager.recent.utils

import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.thread.ThreadPriority
import com.filemanager.common.thread.ThreadType
import com.filemanager.common.utils.SdkUtils
import com.oplus.filemanager.recent.entity.recent.ExpandGroupItemEntity
import com.oplus.filemanager.recent.entity.recent.LoadResultBean
import com.oplus.filemanager.recent.task.*
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * RecentFileObserver的单元测试类
 * 用于测试RecentFileObserver的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RecentFileObserverTest {
    // 被测试的RecentFileObserver实例
    private lateinit var observer: RecentFileObserver
    // 模拟的RecentDataHelper
    private val mockRecentDataHelper: RecentDataHelper = mockk(relaxed = true)
    // 模拟的回调接口
    private val mockLoadCallback: RecentLoadCallback = mockk(relaxed = true)
    // 模拟的线程管理器
    private val mockThreadManager: ThreadManager = mockk(relaxed = true)
    // 模拟的SDK工具类
    private val mockSdkUtils: SdkUtils = mockk(relaxed = true)
    // 模拟的媒体数据库任务(R版本)
    private val mockLoadMediaTaskR: LoadMediaDBTaskR = mockk(relaxed = true)
    // 模拟的媒体数据库任务(Q版本)
    private val mockLoadMediaTaskQ: LoadMediaDBTaskQ = mockk(relaxed = true)
    // 模拟的缓存数据库任务(R版本)
    private val mockLoadCacheTaskR: LoadCacheDBTaskR = mockk(relaxed = true)
    // 模拟的缓存数据库任务(Q版本)
    private val mockLoadCacheTaskQ: LoadCacheDBTaskQ = mockk(relaxed = true)
    // 模拟的本地文件加载任务
    private val mockLoadLocalTask: LoadLocalTask = mockk(relaxed = true)
    // 模拟的分组实体列表
    private val mockGroupEntities: List<ExpandGroupItemEntity> = mockk(relaxed = true)

    /**
     * 测试前的准备工作
     * 1. 模拟各种依赖对象
     * 2. 设置模拟对象的默认行为
     */
    @Before
    fun setUp() {
        // 模拟RecentDataHelper单例
        mockkObject(RecentDataHelper)
        // 模拟ThreadManager单例
        mockkObject(ThreadManager)
        // 模拟SdkUtils静态方法
        mockkStatic(SdkUtils::class)
        // 设置RecentDataHelper.instance返回模拟对象
        every { RecentDataHelper.instance } returns mockRecentDataHelper
        // 设置ThreadManager.sThreadManager返回模拟对象
        every { ThreadManager.sThreadManager } returns mockThreadManager
        // 设置SdkUtils.isAtLeastR()默认返回true(模拟R版本)
        every { SdkUtils.isAtLeastR() } returns true
        // 模拟各种任务的构造函数
        mockkConstructor(LoadMediaDBTaskR::class)
        mockkConstructor(LoadMediaDBTaskQ::class)
        mockkConstructor(LoadCacheDBTaskR::class)
        mockkConstructor(LoadCacheDBTaskQ::class)
        mockkConstructor(LoadLocalTask::class)
        // 创建被测试的RecentFileObserver实例
        observer = RecentFileObserver()
    }

    /**
     * 测试后的清理工作
     * 解除所有模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试未加载时调用loadCancel的情况
     * 预期: 不会调用任何任务的stop方法
     */
    @Test
    fun testLoadCancelWhenNotLoading() {
        observer.loadCancel()
        // 验证没有调用任何任务的stop方法
        verify(exactly = 0) { mockLoadMediaTaskR.stop() }
        verify(exactly = 0) { mockLoadCacheTaskR.stop() }
        verify(exactly = 0) { mockLoadLocalTask.stop() }
    }

    /**
     * 测试加载缓存数据时调用loadCancel的情况
     * 预期: 会调用缓存任务的stop方法
     */
    @Test
    fun testLoadCancelWhenCacheLoading() {
        // 设置缓存任务run方法的行为
        every { anyConstructed<LoadCacheDBTaskR>().run() } returns Unit
        // 开始加载缓存数据
        observer.loadRecentData(RecentDataHelper.TYPE_CACHE, null, mockLoadCallback)
        // 取消加载
        observer.loadCancel()
        // 验证调用了缓存任务的stop方法
        verify { anyConstructed<LoadCacheDBTaskR>().stop() }
        // 验证isLoading状态变为false
        assertFalse(observer.isLoading())
    }

    /**
     * 测试加载本地数据时调用loadCancel的情况
     * 预期: 会调用本地任务的stop方法
     */
    @Test
    fun testLoadCancelWhenLocalLoading() {
        // 设置本地任务run方法的行为
        every { anyConstructed<LoadLocalTask>().run() } returns Unit
        // 开始加载本地数据
        observer.loadRecentData(RecentDataHelper.TYPE_LOCAL, mockGroupEntities, mockLoadCallback)
        // 取消加载
        observer.loadCancel()
        // 验证调用了本地任务的stop方法
        verify { anyConstructed<LoadLocalTask>().stop() }
        // 验证isLoading状态变为false
        assertFalse(observer.isLoading())
    }

    /**
     * 测试destroy方法
     * 预期: 会从RecentDataHelper中移除观察者
     */
    @Test
    fun testDestroy() {
        observer.destroy()
        // 验证调用了deleteObserver方法
        verify { mockRecentDataHelper.deleteObserver(observer) }
    }

    /**
     * 测试使用null回调加载数据的情况
     * 预期: 不会启动任何任务
     */
    @Test
    fun testLoadRecentDataWithNullCallback() {
        observer.loadRecentData(RecentDataHelper.TYPE_MEDIA, null, null)
        // 验证没有调用媒体任务的run方法
        verify(exactly = 0) { anyConstructed<LoadMediaDBTaskR>().run() }
    }

    /**
     * 测试加载缓存数据(R版本)的情况
     * 预期: 会通过线程管理器执行任务
     */
    @Test
    fun testLoadRecentDataCacheR() {
        // 设置缓存任务run方法的行为
        every { anyConstructed<LoadCacheDBTaskR>().run() } returns Unit
        // 开始加载缓存数据
        observer.loadRecentData(RecentDataHelper.TYPE_CACHE, null, mockLoadCallback)
        // 验证通过线程管理器执行了任务
        verify { mockThreadManager.execute(any<FileRunnable>(), ThreadType.LOADER_THREAD, ThreadPriority.HIGH) }
        // 验证isLoading状态为true
        assertTrue(observer.isLoading())
    }

    /**
     * 测试加载缓存数据(Q版本)的情况
     * 预期: 会通过线程管理器执行任务
     */
    @Test
    fun testLoadRecentDataCacheQ() {
        // 设置SDK版本为Q
        every { SdkUtils.isAtLeastR() } returns false
        // 设置缓存任务run方法的行为
        every { anyConstructed<LoadCacheDBTaskQ>().run() } returns Unit
        // 开始加载缓存数据
        observer.loadRecentData(RecentDataHelper.TYPE_CACHE, null, mockLoadCallback)
        // 验证通过线程管理器执行了任务
        verify { mockThreadManager.execute(any<FileRunnable>(), ThreadType.LOADER_THREAD, ThreadPriority.HIGH) }
        // 验证isLoading状态为true
        assertTrue(observer.isLoading())
    }

    /**
     * 测试加载本地数据的情况
     * 预期: 会通过线程管理器执行任务
     */
    @Test
    fun testLoadRecentDataLocal() {
        // 设置本地任务run方法的行为
        every { anyConstructed<LoadLocalTask>().run() } returns Unit
        // 开始加载本地数据
        observer.loadRecentData(RecentDataHelper.TYPE_LOCAL, mockGroupEntities, mockLoadCallback)
        // 验证通过线程管理器执行了任务
        verify { mockThreadManager.execute(any<FileRunnable>(), ThreadType.LOADER_THREAD, ThreadPriority.HIGH) }
        // 验证isLoading状态为true
        assertTrue(observer.isLoading())
    }

    /**
     * 测试update方法在回调为null时的情况
     * 预期: 不会调用任何回调方法
     */
    @Test
    fun testUpdateWithNullCallback() {
        // 调用update方法
        observer.update(mockRecentDataHelper, LoadResultBean(0, 0, emptyList()))
        // 验证没有调用回调方法
        verify(exactly = 0) { mockLoadCallback.loadSucc(any(), any()) }
    }
}