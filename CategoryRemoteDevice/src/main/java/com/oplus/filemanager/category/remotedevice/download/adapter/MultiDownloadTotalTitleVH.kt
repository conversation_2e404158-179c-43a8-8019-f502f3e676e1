/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : MultiDownloadFileTitleVH
 * * Description : 多个下载文件标题的VH
 * * Version     : 1.0
 * * Date        : 2024/12/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.adapter

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.annotation.LayoutRes
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseSelectionViewHolder
import com.oplus.filemanager.category.remotedevice.R

class MultiDownloadTotalTitleVH(convertView: View) : BaseSelectionViewHolder(convertView) {

    companion object {
        private const val TAG = "MultiDownloadTotalTitleVH"

        @LayoutRes
        fun layoutId(): Int {
            return R.layout.layout_download_total_title
        }
    }

    val fileCountTv = convertView.findViewById<TextView>(R.id.tv_multi_file_count)
    val fileSizeTv = convertView.findViewById<TextView>(R.id.tv_multi_file_size)

    fun loadData(context: Context, fileCount: Int, totalSize: String) {
        fileCountTv?.text = context.resources.getQuantityString(com.filemanager.common.R.plurals.file_count, fileCount, fileCount)
        fileSizeTv?.text = context.resources.getString(com.filemanager.common.R.string.multi_file_size, totalSize)
        updateDividerVisible(fileCount, 0)
    }

    override fun drawDivider(): Boolean {
        return true
    }

    override fun getDividerStartAlignView(): View {
        return fileCountTv
    }

    override fun getDividerEndInset(): Int {
        return MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)
    }
}